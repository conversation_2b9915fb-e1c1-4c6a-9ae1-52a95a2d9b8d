ENV = "development"

VITE_ALOVA_TIPS=0
# 资源公共路径,需要以 /开头和结尾
VITE_PUBLIC_PATH = '/'

# 是否hash路由模式
VITE_USE_HASH = false

# base api
VITE_BASE_API = 'https://api.github.com'




# 是否启用代理(只对本地vite server生效，开启MOCK时可关闭代理)
VITE_USE_PROXY = true

# 代理类型(跟启动和构建环境无关)  'dev' | 'test' | 'prod'
VITE_PROXY_TYPE = 'dev'

VITE_BUGLY_ANDROID_APP_ID=a8eeff8062
# VITE_BUGLY_ANDROID_APP_ID=f53cc3e3f1
VITE_BUGLY_IOS_APP_ID=8745864a9d
VITE_HDS_BASE_AUTH="Basic aGRzOjk4NTlmZDc5MzZiMDRkYmI5OGMwMzUyMDJkZmI0YjQz"
VITE_MERCHANT_BASE_AUTH="Basic cnJzamtfbWVyY2hhbnRfY2VudGVyOmVkMF9nb1VJeFlFdjBaSklOSUdjQzVvMGdHaEJZVQ"
# VITE_HDS_BASE_API='https://console.rrsjk.com/hdsapi'
# VITE_MERCHANT_BASE_API='https://console.rrsjk.com/merchant'
VITE_HDS_BASE_API='http://operation.xiaoxianglink.com/hdsapi'
VITE_MERCHANT_BASE_API='http://operation.xiaoxianglink.com/merchant'
VITE_AUTH_BASE_API='https://console.rrsjk.com'


