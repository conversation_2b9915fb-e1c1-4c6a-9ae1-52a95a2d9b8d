import type { Option } from '@/types/common'
import { getRegionList } from '@/api/common'

export function useRegion() {
  let addressList: any
  const provinceOptions = ref<Option[]>([])
  const provinceMap = ref<Record<string, string>>({})
  const cityOptions = ref<Option[]>([])
  const cityMap = ref<Record<string, string>>({})
  const regionOptions = ref<Option[]>([])
  const regionMap = ref<Record<string, string>>({})

  onMounted(() => {
    getRegionList().then(res => {
      addressList = res
      provinceMap.value = res.province_list
      provinceOptions.value = Object.entries(res.province_list).map(([key, value]) => ({
        label: value as string,
        value: key,
      }))
    })
  })

  const setCity = (value: string) => {
    const tempMap: Record<string, string> = {}
    const tempOption: Option[] = []
    const str = value.substring(0, 2)
    const city_list = addressList.city_list
    if (!city_list) {
      cityMap.value = tempMap
      cityOptions.value = tempOption
      return
    }
    for (let key in city_list) {
      if (str === String(key).substring(0, 2)) {
        tempMap[key] = city_list[key]
        tempOption.push({
          label: city_list[key],
          value: key,
        })
      }
    }
    cityMap.value = tempMap
    cityOptions.value = tempOption
    regionMap.value = {}
    regionOptions.value = []
  }

  const setRegion = (value: string) => {
    const tempMap: Record<string, string> = {}
    const tempOption: Option[] = []
    const str = value.substring(0, 4)
    const region_list = addressList.county_list
    if (!region_list) {
      regionMap.value = tempMap
      regionOptions.value = tempOption
      return
    }
    for (let key in region_list) {
      if (str === String(key).substring(0, 4)) {
        tempMap[key] = region_list[key]
        tempOption.push({
          label: region_list[key],
          value: key,
        })
      }
    }
    regionMap.value = tempMap
    regionOptions.value = tempOption
  }

  return {
    provinceOptions,
    provinceMap,
    setCity,
    cityOptions,
    cityMap,
    setRegion,
    regionOptions,
    regionMap,
  }
}
