<script setup lang="ts">
import { ref } from 'vue'
import { navigateTo, navigateBack } from '@uni-helper/uni-promises'

const questions = [
  '如何新建故障工单？',
  '海尔新能源公司简介？',
  '逆变器常见的故障及处理措施？',
  '如何查询租金？',
]

const suggestedTopics = ['每日热门', '文案生成', '电站新闻']

const chatLog = ref([
  {
    id: 1,
    type: 'question',
    text: '如何新建故障工单？',
  },
  {
    id: 2,
    type: 'answer',
    text: '故障工单的创建分为两种情况： 故障工单任务：由总部或分中心下发给代理商，代理商指派给服务并。\n故障/人工提报/其他类任务：由服务兵账号APP端创建，具体操作路径为：打开APP端 进入【工作台】\n点击【待处理】-【新建工单】',
    actions: {
      retry: true,
      feedback: true,
    },
  },
])

function handleClose() {
  navigateBack({ delta: 1 }).catch(() => {
    navigateTo({ url: '/pages/index/index' })
  })
}
</script>

<template>
  <view class="service-page">
    <view class="service-page__header">
      <view class="header-title">
        <text class="main-title">光小智</text>
        <text class="sub-title">海尔新能源AI客服</text>
      </view>
      <view class="close-button" @click="handleClose">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <scroll-view scroll-y class="service-page__content">
      <view class="prompt-section">
        <text class="prompt-title">Hi，试着问我</text>
        <view class="question-list">
          <view v-for="(question, index) in questions" :key="index" class="question-item">
            <text class="question-text">{{ question }}</text>
            <view class="send-icon-container">
              <image src="/static/service/sending.webp" class="send-icon" mode="aspectFit" />
            </view>
          </view>
        </view>
      </view>

      <view class="refresh-section">
        <view class="refresh-button">
          <text class="refresh-icon">↻</text>
          <text class="refresh-text">换一换</text>
        </view>
      </view>

      <view class="chat-log-area">
        <view
          v-for="message in chatLog"
          :key="message.id"
          class="chat-message"
          :class="[`chat-message--${message.type}`]"
        >
          <view class="message-bubble" :class="[`message-bubble--${message.type}`]">
            <text class="message-text">{{ message.text }}</text>
            <view v-if="message.type === 'answer' && message.actions" class="message-actions">
              <view v-if="message.actions.retry" class="action-button retry-button">
                <text class="action-icon">↺</text>
                <text class="action-text">重新回答</text>
              </view>
              <view v-if="message.actions.feedback" class="action-button feedback-buttons">
                <text class="action-icon">👍</text>
                <view class="separator" />
                <text class="action-icon">👎</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="service-page__footer">
      <scroll-view scroll-x class="suggested-topics">
        <view v-for="(topic, index) in suggestedTopics" :key="index" class="topic-button">
          <text class="topic-text">{{ topic }}</text>
        </view>
      </scroll-view>
      <view class="input-area">
        <input
          type="text"
          placeholder="有问题尽管问我"
          class="input-field"
          placeholder-class="input-placeholder"
        />
        <view class="send-button-main">
          <image src="/static/service/send.svg" class="send-icon-main" mode="aspectFit" />
        </view>
      </view>
      <image src="/static/service/service-logo.webp" class="topic-icon" mode="aspectFit" />
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "智能客服"
  }
}
</route>

<style scoped lang="scss">
.service-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(147deg, #e3edff, #fafcff 41%);
  overflow: hidden;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    padding-top: var(--status-bar-height);
    position: relative;
    z-index: 10;

    .header-title {
      display: flex;
      flex-direction: column;
    }

    .main-title {
      font-family: 'Alimama FangYuanTi VF', sans-serif;
      font-size: 20px;
      color: #3d3d3d;
      font-weight: 500;
    }

    .sub-title {
      font-family: 'PingFang SC', sans-serif;
      font-size: 7px;
      color: #3d3d3d;
    }

    .close-button {
      padding: 8px;
    }

    .close-icon {
      font-size: 18px;
      color: #333333;
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
    position: relative;
    box-sizing: border-box;
  }

  .prompt-section {
    background-color: #ffffff;
    border-radius: 18px;
    padding: 17px 20px;
    margin-top: 10px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .prompt-title {
    font-family: 'PingFang SC', sans-serif;
    font-size: 20px;
    color: #3d3d3d;
    font-weight: bold;
    display: block;
    margin-bottom: 12px;
  }

  .question-list {
    display: flex;
    flex-direction: column;
  }

  .question-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }
  }

  .question-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #3d3d3d;
    flex: 1;
  }

  .send-icon-container {
    background-color: #eaf3ff;
    border-radius: 4px;
    width: 27px;
    height: 27px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
  }

  .send-icon {
    width: 18px;
    height: 18px;
  }

  .refresh-section {
    display: flex;
    justify-content: flex-start;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .refresh-button {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    padding: 3px 8px;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .refresh-icon {
    font-size: 14px;
    color: #a8abb2;
    margin-right: 4px;
  }

  .refresh-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #a8abb2;
  }

  .action-button {
    display: flex;
    align-items: center;
    padding: 3px 8px;
    background-color: #ffffff;
    border-radius: 9px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
  }

  .action-icon {
    font-size: 14px;
    color: #19385d;
  }

  .action-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 11px;
    color: #19385d;
    margin-left: 4px;
  }

  .feedback-buttons {
    .action-icon {
      padding: 0 5px;
    }
    .separator {
      width: 1px;
      height: 14px;
      background-color: #d8d8d8;
      margin: 0 5px;
    }
  }

  .chat-log-area {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding-top: 10px;
    padding-bottom: 20px;
  }

  .chat-message {
    display: flex;
    width: 100%;

    &--question {
      justify-content: flex-end;
    }

    &--answer {
      justify-content: flex-start;
    }
  }

  .message-bubble {
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
    position: relative;

    &--question {
      background: linear-gradient(106deg, rgba(182, 232, 255, 0.8), rgba(181, 214, 255, 0.93) 97%);
      color: #19385d;
      border-radius: 13px 13px 0px 13px;

      .message-text {
        line-height: 1;
        font-weight: 500;
      }
    }

    &--answer {
      background-color: #ffffff;
      color: #19385d;
      border-radius: 18px 18px 18px 5px;
    }
  }

  .message-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .message-actions {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    align-items: center;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #eaeaea;

    .action-button {
      padding: 2px 6px;
      background-color: transparent;
      box-shadow: none;
    }

    .action-icon {
      font-size: 13px;
    }

    .action-text {
      font-size: 10px;
    }
    .feedback-buttons .separator {
      background-color: #d1d1d1;
    }
  }

  &__footer {
    padding: 10px 20px;
    background: linear-gradient(176deg, #eaf3ff, #ffffff 98%);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    box-shadow: 0px 0px 100px 0px rgba(64, 150, 254, 0.25);
    position: relative;
    z-index: 10;
    padding-bottom: calc(env(safe-area-inset-bottom) + 12px);
  }

  .suggested-topics {
    white-space: nowrap;
    margin-bottom: 10px;
    margin-left: 80px;
    padding-right: 20px;
    box-sizing: border-box;
  }

  .topic-button {
    border-radius: 15px;
    box-shadow: 0px 0px 8px 0px rgba(64, 150, 254, 0.03);
    display: inline-block;
    vertical-align: middle;
    background-color: #ffffff;
    padding: 4px 12px;
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
  }

  .topic-icon {
    width: 72px;
    height: 64px;
    margin-right: 6px;
    position: absolute;
    bottom: 42px;
    left: 24px;
    z-index: -1;
  }

  .topic-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #4b4b4b;
    font-weight: 500;
  }

  .input-area {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 25px;
    padding: 5px 5px 5px 15px;
    box-shadow: 0px 0px 10px 0px rgba(64, 150, 254, 0.06);
  }

  .input-field {
    flex: 1;
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: #3d3d3d;
    height: 30px;
    line-height: 30px;
  }

  .input-placeholder {
    color: #a8abb2;
  }

  .send-button-main {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
  }

  .send-icon-main {
    width: 21px;
    height: 21px;
  }
}
</style>
