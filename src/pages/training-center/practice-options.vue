<script setup lang="ts">
import { ref } from 'vue'

const selectedDifficulty = ref('初级')
const difficulties = ['初级', '中级', '高级']

const selectedQuestionTypes = ref('单选题') // Changed from array to string
const questionTypes = ['单选题', '多选题', '判断题', '全部']

const selectedPracticeMode = ref('顺序练习')
const practiceModes = ['顺序练习', '随机练习']

const practiceOnlyWrong = ref(false)

const selectedQuestionCount = ref('10题')
const questionCounts = ['10题', '20题', '30题', '50题', '100题', '全部']

// selectOption function is removed as v-model on wd-radio-group handles updates.
</script>

<template>
  <view class="practice-options-page">
    <scroll-view :scroll-y="true" class="page-scroll-content">
      <view class="info-card">
        <view class="info-item">
          <view class="info-icon-placeholder">ICON</view>
          <text class="info-label">总题量：</text>
          <text class="info-value">500题</text>
        </view>
        <view class="info-item">
          <view class="info-icon-placeholder">ICON</view>
          <text class="info-label">平均正确率：</text>
          <text class="info-value">76%</text>
        </view>
      </view>

      <view class="selection-card">
        <view class="selection-group">
          <text class="group-title">考试等级</text>
          <wd-radio-group v-model="selectedDifficulty" shape="button" custom-class="radio-group-row">
            <wd-radio
              v-for="item in difficulties"
              :key="item"
              :value="item"
              custom-class="radio-button"
            >
              {{ item }}
            </wd-radio>
          </wd-radio-group>
        </view>

        <view class="selection-group">
          <text class="group-title">题型选择</text>
          <wd-radio-group v-model="selectedQuestionTypes" shape="button" custom-class="radio-group-grid two-cols">
            <wd-radio
              v-for="item in questionTypes"
              :key="item"
              :value="item"
              custom-class="radio-button"
            >
              {{ item }}
            </wd-radio>
          </wd-radio-group>
        </view>

        <view class="selection-group">
          <text class="group-title">练习模式</text>
          <wd-radio-group v-model="selectedPracticeMode" shape="button" custom-class="radio-group-grid two-cols">
            <wd-radio
              v-for="item in practiceModes"
              :key="item"
              :value="item"
              custom-class="radio-button"
            >
              {{ item }}
            </wd-radio>
          </wd-radio-group>
        </view>

        <view class="selection-group switch-group">
          <text class="group-title">仅练习错题</text>
          <wd-switch v-model="practiceOnlyWrong" />
        </view>
      </view>

      <view class="selection-card">
        <view class="selection-group">
          <text class="group-title">练习题量</text>
          <wd-radio-group v-model="selectedQuestionCount" shape="button" custom-class="radio-group-grid three-cols">
            <wd-radio
              v-for="item in questionCounts"
              :key="item"
              :value="item"
              custom-class="radio-button"
            >
              {{ item }}
            </wd-radio>
          </wd-radio-group>
        </view>
      </view>
    </scroll-view>

    <view class="bottom-bar">
      <wd-button type="primary" custom-class="start-button" block size="large">开始练习</wd-button>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "光伏安装题库"
  }
}
</route>

<style scoped lang="scss">
.practice-options-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f5;
}

.page-scroll-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 95px; // Height of bottom bar
}

.info-card {
  background-color: #ffffff;
  padding: 18px 17px; // Adjusted to match DSL group relativeX
  margin: 0; // DSL shows BankInfo at Y:88, Navbar is ~44, so this is directly below.
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-icon-placeholder {
  width: 16px;
  height: 16px;
  background-color: #e0e0e0; // Placeholder color
  border-radius: 50%;
  margin-right: 8px;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
}

.info-label {
  font-size: 14px;
  color: #666666;
}

.info-value {
  font-size: 14px;
  color: #409eff; // As per DSL paint_281:03402
  margin-left: 4px;
}

.selection-card {
  background-color: #ffffff;
  border-radius: 8px;
  margin: 15px 16px 0; // DSL relativeX:16, relativeY:159 (159-88-56 = 15px top margin)
  padding: 9px 16px; // DSL group relativeX:15.5, relativeY:9.44
}

.selection-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-title {
  font-size: 16px;
  color: #333333;
  display: block;
  margin-bottom: 12px; // Approx from DSL
}

.options-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  &.three-cols {
    grid-template-columns: repeat(3, 1fr);
  }
}

// Styling for wd-radio-group and wd-radio with shape="button"
:deep(.radio-group-row) {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  .wd-radio {
    margin: 0 !important; // Override default margin if any
    flex-grow: 1; // Allow radio buttons in a row to grow and fill available space
  }
}

:deep(.radio-group-grid) {
  display: grid;
  gap: 10px;
  .wd-radio {
    margin: 0 !important; // Override default margin if any
  }
  &.two-cols {
    grid-template-columns: repeat(2, 1fr);
  }
  &.three-cols {
    grid-template-columns: repeat(3, 1fr);
  }
}

:deep(.radio-button) {
  .wd-radio__label {
    width: 100%;
    text-align: center;
    font-size: 14px !important;
    height: 36px !important;
    line-height: 36px !important;
    border-radius: 18px !important;
    padding: 0 !important; // Remove default padding
    margin-right: 0 !important; // Override default margin if any
    box-sizing: border-box;
  }

  // Unchecked state (default for shape="button")
  &.wd-radio .wd-radio__label {
    color: #666666 !important;
    border: 1px solid #d9d9d9 !important;
    background-color: #ffffff !important;
  }

  // Checked state for shape="button"
  &.is-checked .wd-radio__label {
    color: #ffffff !important;
    background-color: #1677ff !important; // DSL paint_212:11696 for active option
    border-color: #1677ff !important;
  }
}

.switch-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px; // Approx from DSL
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 10px 16px;
  padding-bottom: calc(10px + env(safe-area-inset-bottom)); // For iPhone X notch
  border-top: 1px solid #f4f4f4; // DSL paint_295:19528
  z-index: 100;
}

:deep(.start-button) {
  &.wd-button--primary {
    background-color: #37acfe !important; // DSL paint_285:12475
    border-color: #37acfe !important;
    color: #ffffff !important;
    border-radius: 7.5px !important;
    font-size: 15px !important;
    height: 44px !important;
  }
}
</style>
