<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
import { getAnswerDetail } from '@/api/exam'
import type { ExamAnswerDetail } from '@/types/api/Exam'
import dayjs from 'dayjs'

const toast = useToast()
const isLoading = ref(true)
const examDetail = ref<ExamAnswerDetail | null>(null)

// 从路由参数获取 answerId
const answerId = ref<number | null>(null)

// 计算属性
const score = computed(() => examDetail.value?.score || 0)
const passScore = computed(() => examDetail.value?.passScore || 60)
const totalQuestions = computed(() => examDetail.value?.questions?.length || 0)

// 计算正确和错误题数
const correctQuestions = computed(() => {
  if (!examDetail.value?.questions || !examDetail.value?.answers) return 0

  let correct = 0
  examDetail.value.questions.forEach(question => {
    const userAnswer = examDetail.value?.answers[question.id]
    if (userAnswer && question.answer) {
      // 处理多选题答案比较
      if (Array.isArray(userAnswer)) {
        const sortedUserAnswer = userAnswer.sort().join(',')
        const sortedCorrectAnswer = question.answer.split(',').sort().join(',')
        if (sortedUserAnswer === sortedCorrectAnswer) {
          correct++
        }
      } else {
        // 单选题答案比较
        if (userAnswer === question.answer) {
          correct++
        }
      }
    }
  })
  return correct
})

const wrongQuestions = computed(() => totalQuestions.value - correctQuestions.value)

// 计算考试用时
const examTime = computed(() => {
  if (!examDetail.value?.startTime || !examDetail.value?.endTime) return '未知'

  const start = dayjs(examDetail.value.startTime)
  const end = dayjs(examDetail.value.endTime)
  const duration = end.diff(start, 'second')

  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = duration % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分${seconds}秒`
  } else if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  } else {
    return `${seconds}秒`
  }
})

// 计算平均每题用时
const avgTimePerQuestion = computed(() => {
  if (!examDetail.value?.startTime || !examDetail.value?.endTime || totalQuestions.value === 0) return '未知'

  const start = dayjs(examDetail.value.startTime)
  const end = dayjs(examDetail.value.endTime)
  const totalSeconds = end.diff(start, 'second')
  const avgSeconds = Math.round(totalSeconds / totalQuestions.value)

  if (avgSeconds >= 60) {
    const minutes = Math.floor(avgSeconds / 60)
    const seconds = avgSeconds % 60
    return `${minutes}分${seconds}秒`
  } else {
    return `${avgSeconds}秒`
  }
})

// 判断是否通过考试
const isPassed = computed(() => score.value >= passScore.value)

// 获取考试详情
async function fetchExamDetail(id: number) {
  isLoading.value = true
  try {
    const data = await getAnswerDetail({ answerId: id })
    examDetail.value = data

    // 动态设置页面标题
    if (data.examName) {
      uni.setNavigationBarTitle({
        title: data.examName
      })
    }
  } catch (error) {
    toast.error('获取考试结果失败')
  } finally {
    isLoading.value = false
  }
}

function onViewWrongQuestions() {
  if (wrongQuestions.value === 0) {
    toast.success('恭喜！您没有错题')
    return
  }

  // 跳转到错题页面
  uni.navigateTo({
    url: `/pages/training-center/exam/wrong-questions?answerId=${answerId.value}`
  })
}

function onComplete() {
  uni.navigateBack()
}

onLoad((options) => {
  if (options?.answerId) {
    answerId.value = parseInt(options.answerId)
    fetchExamDetail(answerId.value)
  } else {
    toast.error('缺少考试ID参数')
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
})
</script>

<template>
  <view class="exam-result-page">
    <wd-toast />

    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <wd-loading size="24px" />
    </view>

    <!-- 考试结果 -->
    <view v-else-if="examDetail" class="result-content">
      <view class="score-card">
        <view class="card-title score-card__title">
          考试结果
        </view>
        <view class="score-card__content">
          <view class="success-icon-container">
            <view class="success-icon__outer-circle" :class="{ 'failed': !isPassed }">
              <view class="success-icon__inner-circle" :class="{ 'failed': !isPassed }">
                <view v-if="isPassed" class="i-carbon-checkmark text-[#1677FF] text-[24px]" />
                <view v-else class="i-carbon-close text-[#f5222d] text-[24px]" />
              </view>
            </view>
          </view>
          <view class="score-display">
            <text class="score-value" :class="{ 'failed': !isPassed }">{{ score }}</text>
            <text class="score-unit">分</text>
          </view>
          <text class="congrats-message" :class="{ 'failed': !isPassed }">
            {{ isPassed ? '恭喜您通过了考试！' : '很遗憾，您未通过考试' }}
          </text>
          <text class="pass-score-info">及格分数：{{ passScore }}分</text>
        </view>
      </view>

      <view class="stats-card">
        <view class="card-title stats-card__title">
          答题统计
        </view>
        <view class="stats-card__content">
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--correct">
              <text class="stat-item__value stat-item__value--correct">{{ correctQuestions }}</text>
            </view>
            <text class="stat-item__label">正确题数</text>
          </view>
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--wrong">
              <text class="stat-item__value stat-item__value--wrong">{{ wrongQuestions }}</text>
            </view>
            <text class="stat-item__label">错误题数</text>
          </view>
          <view class="stat-item">
            <view class="stat-item__circle stat-item__circle--total">
              <text class="stat-item__value stat-item__value--total">{{ totalQuestions }}</text>
            </view>
            <text class="stat-item__label">总题数</text>
          </view>
        </view>
      </view>

      <view class="duration-card">
        <view class="duration-card__row">
          <text class="duration-card__label">考试用时</text>
          <text class="duration-card__value">{{ examTime }}</text>
        </view>
        <view class="duration-card__row">
          <text class="duration-card__label-avg">平均每题用时：</text>
          <text class="duration-card__value-avg">{{ avgTimePerQuestion }}</text>
        </view>
      </view>

      <view class="bottom-actions">
        <view
          class="action-button action-button--wrong"
          @click="onViewWrongQuestions"
        >
          查看错题 ({{ wrongQuestions }})
        </view>
        <view
          class="action-button action-button--complete"
          @click="onComplete"
        >
          完成
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <text>无法加载考试结果，请返回重试。</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "考试结果",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.exam-result-page {
  background-color: #f7f7f5;
  min-height: 100vh;
  padding-bottom: calc(95px + env(safe-area-inset-bottom));
  padding-top: 1px;
  box-sizing: border-box;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: #666666;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-title {
  font-weight: 500;
}

.score-card {
  background-color: #ffffff;
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;

  &__title {
    background-color: #f0f7ff;
    padding: 11px 16px;
    color: #1677ff;
    font-size: 16px;
  }

  &__content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.success-icon-container {
  margin-bottom: 15px;
}

.success-icon__outer-circle {
  background-color: #e6f7ff;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.failed {
    background-color: #fff1f0;
  }
}

.success-icon__inner-circle {
  background-color: #bae7ff;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.failed {
    background-color: #ffccc7;
  }
}

.score-display {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-end;
}

.score-value {
  font-family: 'PingFang SC', sans-serif;
  color: #1677ff;
  font-size: 24px;
  font-weight: 500;

  &.failed {
    color: #f5222d;
  }
}

.score-unit {
  font-family: 'PingFang SC', sans-serif;
  color: #666666;
  font-size: 15px;
  margin-left: 5px;
}

.congrats-message {
  font-family: 'PingFang SC', sans-serif;
  color: #666666;
  font-size: 15px;
  margin-bottom: 10px;

  &.failed {
    color: #f5222d;
  }
}

.pass-score-info {
  font-family: 'PingFang SC', sans-serif;
  color: #999999;
  font-size: 13px;
}

.stats-card {
  background-color: #ffffff;
  margin: 16px;
  border-radius: 6px;
  overflow: hidden;

  &__title {
    background-color: #f0f7ff;
    padding: 11px 16px;
    color: #1677ff;
    font-size: 16px;
  }

  &__content {
    padding: 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;

  &__circle {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;

    &--correct {
      background-color: #e6f7ff;
    }
    &--wrong {
      background-color: #fff1f0;
    }
    &--total {
      background-color: #f0f7ff;
    }
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 18px;
    &--correct {
      color: #1677ff;
    }
    &--wrong {
      color: #f5222d;
    }
    &--total {
      color: #666666;
    }
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    color: #666666;
    font-size: 13px;
  }
}

.duration-card {
  background-color: #ffffff;
  margin: 16px;
  padding: 16px;
  border-radius: 8px;

  &__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    color: #333333;
    font-size: 15px;
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    color: #1677ff;
    font-size: 15px;
  }

  &__label-avg {
    font-family: 'PingFang SC', sans-serif;
    color: #999999;
    font-size: 13px;
  }

  &__value-avg {
    font-family: 'PingFang SC', sans-serif;
    color: #666666;
    font-size: 13px;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 16px;
  padding-top: 12px;
  border-top: 1px solid #f4f4f4;
}

.action-button {
  font-family: 'PingFang SC', sans-serif;
  box-sizing: border-box;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 7.5px;
  font-size: 15px;

  &--wrong {
    margin-bottom: 12px;
    border: 1px dashed #1677ff;
    color: #1677ff;
  }

  &--complete {
    background-color: #37acfe;
    color: #ffffff;
  }
}

</style>
