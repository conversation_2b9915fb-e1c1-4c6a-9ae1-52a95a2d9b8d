<script lang="ts" setup>
import { useUserStore, useAuthStore } from '@/store'
import { navigateTo } from '@uni-helper/uni-promises'
import { ref } from 'vue'
import { useMessage } from 'wot-design-uni'

const userStore = useUserStore()
const userInfo = userStore.userInfo
const auth = useAuthStore()
const message = useMessage()

const commonFunctions = ref([
  { name: '个人信息', icon: 'i-solar-user-id-broken', url: '/pages/user/info' },
  { name: '修改密码', icon: 'i-mage-edit', url: '/pages/user/modify' },
  { name: '设置', icon: 'i-carbon-settings', url: '/pages/setting/index' },
])

const moreServices = ref([
  { name: '智能客服', icon: 'i-lsicon-service-outline', url: '/pages/service/index' },
  {
    name: '模拟考试',
    icon: 'i-healthicons-i-exam-multiple-choice-outline',
    url: '/pages/training-center/index',
  },
])

const goToPage = (url: string) => {
  navigateTo({
    url,
  })
}

function handleLogoutAccount() {
  message
    .confirm({
      title: '提示',
      msg: '确定要注销账号吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(() => {
      // 注销账号的逻辑可以在这里实现
      // 例如调用注销接口，然后清除本地存储的信息
      auth.cleanToken()
      uni.reLaunch({ url: '/pages/user/login' })
    })
}

function handleLogout() {
  message
    .confirm({
      title: '提示',
      msg: '确定要退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(() => {
      auth.cleanToken()
      uni.reLaunch({ url: '/pages/user/login' })
    })
}

function goAboutUs() {
  navigateTo({
    url: '/pages/my/about'
  })
}
</script>

<template>
  <view class="my-page min-h-screen">
    <view class="header relative">
      <view class="header__gradient-bg absolute inset-0" />
      <view
        class="header__title absolute z-2 text-center w-full text-lg font-medium text-white pt-12"
        >我的</view
      >
      <view class="header__content relative z-1 flex items-center pt-[98px] px-4 pb-4">
        <image :src="'/static/logo.png'" class="header__avatar w-[50px] h-[50px] mr-4" />
        <view class="header__info">
          <text class="header__name block">{{ userInfo?.name }}</text>
          <text class="header__signature block mt-1">{{ userInfo?.phone }}</text>
        </view>
      </view>
    </view>

    <view class="content-section px-4 absolute top-[170px] left-0 right-0 z-2">
      <view class="card common-functions-card mb-4 p-[15px] card-shadow rounded-lg bg-white">
        <text class="card__title block mb-4 text-sm font-medium text-[#4B4B4B]">常用功能</text>
        <view class="card__grid grid grid-cols-4 gap-4">
          <view
            v-for="item in commonFunctions"
            :key="item.name"
            class="card__grid-item flex flex-col items-center"
            @click="goToPage(item.url)"
          >
            <view class="card__icon text-3xl mb-1" :class="item.icon" />
            <text class="card__text text-xs">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <view class="card more-services-card mb-4 p-[15px] card-shadow rounded-lg bg-white">
        <text class="card__title block mb-4 text-sm font-medium text-[#4B4B4B]">更多服务</text>
        <view class="card__grid grid grid-cols-4 gap-4">
          <view
            v-for="item in moreServices"
            :key="item.name"
            class="card__grid-item flex flex-col items-center"
            @click="goToPage(item.url)"
          >
            <view class="card__icon text-3xl mb-1" :class="item.icon" />
            <text class="card__text text-xs">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <view class="action-list rounded-lg overflow-hidden card-shadow bg-white">
        <wd-cell-group border>
          <wd-cell title="关于我们" is-link @click="goAboutUs"> </wd-cell>
          <wd-cell title="注销账号" is-link @click="handleLogoutAccount"> </wd-cell>
          <wd-cell title="退出登录" is-link @click="handleLogout"> </wd-cell>
        </wd-cell-group>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "我的"
  },
  "usingComponents": {
    "wd-cell": "wot-design-uni/components/wd-cell/wd-cell",
    "wd-cell-group": "wot-design-uni/components/wd-cell-group/wd-cell-group",
    "wd-icon": "wot-design-uni/components/wd-icon/wd-icon"
  }
}
</route>

<style scoped lang="scss">
.my-page {
  background-color: #f5f7fa;
}

.header {
  color: white;

  &__gradient-bg {
    background: linear-gradient(180deg, #4b8dfe 0%, #f5f7fa 100%);
    height: 283px;
  }

  &__avatar {
    border-color: #f7f7f5;
    border-radius: 9999px;
    border-width: 2px;
  }

  &__name {
    @apply text-lg font-medium;
  }

  &__signature {
    @apply text-xs mt-1;
    color: white;
  }
}

.card {
  &.card-shadow {
    box-shadow: 5px 5px 10px 5px rgba(0, 0, 0, 0.05);
  }

  &__grid-item {
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }

  &__icon {
    color: #4b4b4b;
  }

  &__text {
    color: #4b4b4b;
  }
}

.action-list {
  &.card-shadow {
    box-shadow: 5px 5px 10px 5px rgba(0, 0, 0, 0.05);
  }

  .action-list__icon {
    @apply mr-2 text-lg;
    color: #4b4b4b;
  }

  :deep(.wd-cell__title) {
    color: #4b4b4b;
    font-size: 14px;
    line-height: 2;
  }

  :deep(.wd-cell) {
    padding: 0px 16px;
  }
}
</style>
