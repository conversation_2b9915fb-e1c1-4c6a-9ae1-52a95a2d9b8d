<script setup lang="ts">
import { useMessage, useToast } from 'wot-design-uni'

const message = useMessage()
const toast = useToast()

function goToServiceAgreement() {
  toast.info('查看服务协议')
}

function goToPrivacyPolicy() {
  toast.info('查看隐私政策')
}
</script>

<template>
  <div class="about-page">
    <wd-cell-group border>
      <wd-cell title="服务协议" is-link custom-class="about-item" @click="goToServiceAgreement" />
      <wd-cell title="隐私政策" is-link custom-class="about-item" @click="goToPrivacyPolicy" />
    </wd-cell-group>
  </div>
</template>

<route lang="json">{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "关于我们"
  }
}</route>

<style scoped lang="scss">
.about-page {
  background-color: white;
  min-height: 100vh;
}

:deep(.about-item) {
  .wd-cell__title {
    color: #303133;
    font-size: 15px;
  }

  .wd-cell__arrow {
    color: #c0c4cc;
  }
}
</style>
