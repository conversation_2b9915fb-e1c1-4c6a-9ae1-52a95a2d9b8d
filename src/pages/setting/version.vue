<script setup lang="ts">
const handleUpdate = () => {
  uni.showToast({
    title: '已经是最新版本',
    icon: 'none',
  })
}
</script>

<template>
  <view class="version-page">
    <view class="version-page__details">
      <view class="version-page__row">
        <text class="version-page__label">当前版本</text>
        <text class="version-page__value">Version 1.0.0</text>
      </view>
      <view class="version-page__row version-page__row--check-info">
        <view class="version-page__check-icon">
          <text class="version-page__check-icon-text">i</text>
        </view>
        <text class="version-page__check-text">检查到最新版本</text>
      </view>
    </view>
    <wd-button type="primary" block :round="false" custom-class="version-page__update-button" @click="handleUpdate">
      更新版本
    </wd-button>
  </view>
</template>

<route lang="json">{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "关于版本"
  }
}</route>

<style scoped lang="scss">
.version-page {
  padding: 20px;
  background-color: #fff;
  min-height: 100vh;

  &__details {
    margin-bottom: 14.4px;
  }

  &__row {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &--check-info {
      justify-content: flex-start;
      margin-top: 5px;
    }
  }

  &__label {
    color: #606266;
    font-size: 15px;
  }

  &__value {
    color: #303133;
    font-size: 15px;
  }

  &__check-icon {
    width: 16px;
    height: 16px;
    border-radius: 9999px;
    background-color: #37ACFE;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
  }

  &__check-icon-text {
    color: #fff;
    font-size: 10px;
    line-height: 1;
  }

  &__check-text {
    color: #37ACFE;
    font-size: 13px;
  }

  &__update-button {
    border-radius: 7.5px !important;
    height: 45px !important;
    font-size: 14px !important;
  }
}
</style>
