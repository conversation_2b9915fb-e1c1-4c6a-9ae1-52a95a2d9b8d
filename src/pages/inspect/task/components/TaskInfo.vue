<template>
  <view class="card m-3 p-4 bg-white rounded-lg shadow-sm">
    <view class="card-title flex items-center mb-3">
      <view class="accent-bar bg-primary w-1 h-4 mr-2"></view>
      <text class="text-sm font-bold text-[#4B4B4B]">任务信息</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">任务编码</text>
      <text class="value">{{ task?.orderCode || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">巡检计划</text>
      <text class="value">{{ task?.planName || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">开始时间</text>
      <text class="value">{{ task?.startDate || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">结束时间</text>
      <text class="value">{{ task?.endDate || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">是否逾期</text>
      <text class="value">{{ task?.overTime ? '是' : '否' }}</text>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  task: {
    type: Object,
    default: () => ({}),
  },
  photos: {
    type: Array,
    default: () => [],
  },
})
</script>

<style scoped>
.detail-row {
  display: flex;
  margin-bottom: 10px;
}

.label {
  color: #999;
  font-size: 14px;
  width: 88px;
}

.value {
  color: #333;
  font-size: 14px;
  flex: 1;
}
</style>
