<script setup lang="ts">
import { useDictStore } from '@/store/modules/dict'
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { pmSpecialFlag, property, subCenterList } from '@/constants/dict'
import type { InspectWorkOrderPageParams } from '@/types/api/Inspect'
import { getInspectionPlanList } from '@/api/inspect'
import { useUserStore } from '@/store'
import { usePageExtend } from '@/composables/usePageExtend'
interface FilterData extends InspectWorkOrderPageParams {}

const internalShow = defineModel<boolean>('modelValue')

const emit = defineEmits<{
  (e: 'apply', filters: FilterData): void
}>()
const dictStore = useDictStore()
const userStore = useUserStore()
const userInfo = userStore.userInfo
const planOptions = ref<any[]>([])
const { options } = usePageExtend()

onMounted(() => {
  dictStore.fetchDict(['inspection_type', 'station_type'])
})

const filters = reactive<FilterData>({
  startDateEnd: undefined,
  startDateBegin: undefined,
  inspectionType: undefined,
  planId: undefined,
  opName: options.opName,
  specialFlag: options.specialFlag,
  stationType: undefined,
})

// 日期转换函数，用于显示日期
const displayDate = (dateStr: string | undefined) => {
  return dateStr || ''
}

const inspectionTypeOptions = computed(() => dictStore.getDictByType('inspection_type') || [])

const stationTypeOptions = computed(() => dictStore.getDictByType('station_type') || [])

const quickTimeRanges = reactive([
  { label: '最近一周', value: 'LAST_WEEK' },
  { label: '最近一月', value: 'LAST_MONTH' },
  { label: '最近三月', value: 'LAST_THREE_MONTHS' },
  { label: '全部', value: 'ALL' },
])
const selectedQuickTime = ref('')

const handleReset = () => {
  filters.startDateEnd = undefined
  filters.startDateBegin = undefined
  filters.inspectionType = undefined
  filters.planId = undefined
  filters.stationType = undefined
  filters.opName = options.opName
  filters.specialFlag = options.specialFlag
  selectedQuickTime.value = ''
  calendarDates.startDate = null
  calendarDates.endDate = null
}

const handleClose = () => {
  internalShow.value = false
}

const handleApply = () => {
  emit('apply', { ...filters })
  internalShow.value = false
}

const selectQuickTime = (value: string) => {
  selectedQuickTime.value = value
  const today = new Date()
  if (value === 'LAST_WEEK') {
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    filters.startDateBegin = formatDate(lastWeek)
    filters.startDateEnd = formatDate(today)
    calendarDates.startDate = lastWeek.getTime()
    calendarDates.endDate = today.getTime()
  } else if (value === 'LAST_MONTH') {
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
    filters.startDateBegin = formatDate(lastMonth)
    filters.startDateEnd = formatDate(today)
    calendarDates.startDate = lastMonth.getTime()
    calendarDates.endDate = today.getTime()
  } else if (value === 'LAST_THREE_MONTHS') {
    const lastThreeMonths = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate())
    filters.startDateBegin = formatDate(lastThreeMonths)
    filters.startDateEnd = formatDate(today)
    calendarDates.startDate = lastThreeMonths.getTime()
    calendarDates.endDate = today.getTime()
  } else if (value === 'ALL') {
    filters.startDateBegin = undefined
    filters.startDateEnd = undefined
    calendarDates.startDate = null
    calendarDates.endDate = null
  }
}

const formatDate = (date: Date) => {
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 日期时间戳转换对象，用于 wd-calendar 组件
const calendarDates = reactive({
  startDate: null as number | null,
  endDate: null as number | null,
})

// 监听 filters 中日期变化，更新 calendarDates
watch(
  () => filters.startDateBegin,
  val => {
    if (val) {
      calendarDates.startDate = new Date(val).getTime()
    } else {
      calendarDates.startDate = null
    }
  },
  { immediate: true },
)

watch(
  () => filters.startDateEnd,
  val => {
    if (val) {
      calendarDates.endDate = new Date(val).getTime()
    } else {
      calendarDates.endDate = null
    }
  },
  { immediate: true },
)

// 监听 calendarDates 变化，更新 filters
watch(
  () => calendarDates.startDate,
  val => {
    if (val) {
      filters.startDateBegin = formatDate(new Date(val))
    } else {
      filters.startDateBegin = undefined
    }
  },
)

watch(
  () => calendarDates.endDate,
  val => {
    if (val) {
      filters.startDateEnd = formatDate(new Date(val))
    } else {
      filters.startDateEnd = undefined
    }
  },
)

const planMap = ref<Record<string, string>>({})

const handleStationTypeChange = (val: any) => {
  if (val.value) {
    getInspectionPlanList({
      stationType: val.value,
      inspectionType: filters.inspectionType,
    }).then(res => {
      planOptions.value = res.map(item => {
        return {
          label: item.planName,
          value: item.id,
        }
      })
      planMap.value = res.reduce((acc: any, item) => {
        acc[item.id!.toString()] = item.planName
        return acc
      }, {})
      filters.planId = undefined
    })
  }
}

const handleInspectionTypeChange = (val: any) => {
  if (val.value && filters.stationType) {
    getInspectionPlanList({
      stationType: filters.stationType,
      inspectionType: val.value,
    }).then(res => {
      planOptions.value = res.map(item => {
        return {
          label: item.planName,
          value: item.id,
        }
      })
      filters.planId = undefined
    })
  }
}
</script>

<template>
  <wd-popup v-model="internalShow" position="bottom" custom-style="height: 72vh">
    <view class="filter-popup">
      <view class="filter-popup__header">
        <text class="filter-popup__title">筛选</text>
        <view class="filter-popup__close" @click="handleClose">
          <text class="i-carbon-close filter-popup__close-icon"></text>
          <text>关闭</text>
        </view>
      </view>

      <view scroll-y class="filter-popup__content">
        <template v-if="!options.planId">
          <view class="filter-section">
            <text class="filter-section__title">电站类型</text>
            <wd-radio-group
              v-model="filters.stationType"
              shape="dot"
              inline
              @change="handleStationTypeChange"
            >
              <wd-radio
                v-for="item in stationTypeOptions"
                :key="item.value"
                :value="item.value"
                custom-class="filter-radio"
              >
                {{ item.label }}
              </wd-radio>
            </wd-radio-group>
          </view>

          <view class="filter-section">
            <text class="filter-section__title">巡检类型</text>
            <wd-radio-group
              v-model="filters.inspectionType"
              shape="dot"
              inline
              @change="handleInspectionTypeChange"
            >
              <wd-radio
                v-for="item in inspectionTypeOptions"
                :key="item.value"
                :value="item.value"
                custom-class="filter-radio"
              >
                {{ item.label }}
              </wd-radio>
            </wd-radio-group>
          </view>
          <view class="filter-section">
            <text class="filter-section__title">巡检计划</text>
            <wd-select-picker
              v-model="filters.planId!"
              use-default-slot
              type="radio"
              :columns="planOptions"
              custom-class="filter-section__picker"
            >
              <text :class="['picker__text', !filters.planId && 'picker__text--placeholder']">{{
                planMap[filters.planId || ''] || '请先选择电站类型'
              }}</text>
              <text class="i-carbon-chevron-down picker__icon"></text>
            </wd-select-picker>
          </view>
        </template>
        <template v-if="userInfo?.userType === 'haier'">
          <view v-if="!options.specialFlag" class="filter-section">
            <text class="filter-section__title">资方</text>
            <wd-select-picker
              v-model="filters.specialFlag!"
              use-default-slot
              type="radio"
              :columns="property"
              custom-class="filter-section__picker"
            >
              <text :class="['picker__text', !filters.specialFlag && 'picker__text--placeholder']">{{
                pmSpecialFlag[filters.specialFlag || ''] || '选择资方'
              }}</text>
              <text class="i-carbon-chevron-down picker__icon"></text>
            </wd-select-picker>
          </view>
          <view v-if="!options.opName" class="filter-section">
            <text class="filter-section__title">运维商</text>
            <wd-input
              v-model="filters.opName"
              placeholder="请输入运维商"
              clearable
              custom-class="filter-section__input"
            />
          </view>
        </template>
        <view class="filter-section">
          <text class="filter-section__title">任务时间</text>
          <view class="time-range">
            <wd-calendar
              v-model="calendarDates.startDate"
              custom-class="time-range__picker"
              :max-date="calendarDates.endDate || undefined"
            >
              <text
                :class="[
                  'time-range__date-text',
                  !filters.startDateBegin && 'time-range__date-text--placeholder',
                ]"
              >
                {{ displayDate(filters.startDateBegin) || '开始日期' }}
              </text>
              <text class="i-carbon-calendar time-range__icon"></text>
            </wd-calendar>
            <text class="time-range__separator">至</text>
            <wd-calendar
              v-model="calendarDates.endDate"
              custom-class="time-range__picker"
              :min-date="calendarDates.startDate || undefined"
            >
              <text
                :class="[
                  'time-range__date-text',
                  !filters.startDateEnd && 'time-range__date-text--placeholder',
                ]"
              >
                {{ displayDate(filters.startDateEnd) || '结束日期' }}
              </text>
              <text class="i-carbon-calendar time-range__icon"></text>
            </wd-calendar>
          </view>
          <view class="quick-time-buttons">
            <wd-button
              v-for="item in quickTimeRanges"
              :round="false"
              :key="item.value"
              size="small"
              :type="selectedQuickTime === item.value ? 'primary' : 'info'"
              custom-class="quick-time-button"
              @click="selectQuickTime(item.value)"
            >
              {{ item.label }}
            </wd-button>
          </view>
        </view>
      </view>

      <view class="filter-popup__footer">
        <view class="filter-popup__button-group">
          <wd-button
            type="info"
            :round="false"
            custom-class="reset-button"
            @click="handleReset"
            plain
          >
            重置
          </wd-button>
          <wd-button
            type="primary"
            :round="false"
            custom-class="submit-button"
            @click="handleApply"
          >
            提交
          </wd-button>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.filter-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f2f2f2;
  }

  &__title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }

  &__close {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #909399;
    gap: 4px;
  }

  &__close-icon {
    font-size: 16px;
  }

  &__content {
    flex: 1;
    padding: 16px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  &__footer {
    padding: 12px 16px;
    border-top: 1px solid #f2f2f2;
    background-color: #fff;
  }

  &__button-group {
    display: flex;
    gap: 12px;
  }
}

.filter-section {
  margin-bottom: 20px;

  &__title {
    display: block;
    font-size: 16px;
    color: #303133;
    margin-bottom: 12px;
  }

  &__input {
    height: 40px;
    padding: 0px 12px;
    box-sizing: border-box;
    border-radius: 8px;
    background: #f5f7fa;
    --wot-input-inner-height: 40px;

    &::after {
      content: none;
    }
  }

  &__picker {
    height: 40px;
    padding: 8px 12px;
    box-sizing: border-box;
    border-radius: 8px;
    background: #f5f7fa;

    :deep(.wd-select-picker__field) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .picker__text {
      color: #303133;
      font-size: 15px;

      &--placeholder {
        color: var(--wot-input-placeholder-color, #c0c4cc);
      }
    }

    .picker__icon {
      color: #c0c4cc;
    }
  }
}

.time-range {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;

  &__picker {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 10px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f5f7fa;
    height: 40px;
    box-sizing: border-box;
    min-width: 0;

    :deep(.wd-calendar__field) {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  &__date-text {
    font-size: 14px;
    color: #606266;

    &--placeholder {
      color: var(--wot-input-placeholder-color, #c0c4cc);
    }
  }

  &__icon {
    font-size: 16px;
    color: #909399;
  }

  &__separator {
    font-size: 14px;
    color: #909399;
  }
}

.quick-time-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
}

:deep(.filter-radio) {
  margin-bottom: 8px; // 增加选项间距
  margin-right: 10px !important; // 覆盖wot-design的内联样式
}

:deep(.submit-button) {
  flex: 1;
  font-size: 16px;
}

:deep(.reset-button) {
  flex: 1;
  font-size: 16px;
}
</style>
