<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref, computed } from 'vue'
import { useMessage, useToast } from 'wot-design-uni'
import Navbar from '@/components/Navbar.vue'
import StationInfo from '../../work-order/components/StationInfo.vue'
import TaskInfo from './components/TaskInfo.vue'
import { useChannel } from '@/composables/useChannel'
import { getInspectionWorkOrder, handleInspectionWorkOrder } from '@/api/inspect'
import type { InspectHandleParams, InspectionWorkOrder } from '@/types/api/Inspect'
import WorkorderHandleForm from './components/WorkorderHandleForm.vue'

const toast = useToast()
const task = ref<InspectionWorkOrder>({})
const pageTitle = ref('')
const showBottomActions = ref(true)

const isHandling = ref(false)

// 添加操作菜单相关状态
const showActionSheet = ref(false)

const actionItems = computed(() => {
  const items = []
  if (task.value.solution?.file) {
    items.push({ name: '预览PDF文件', color: '#3B82F6' })
  }
  if (task.value.solution?.video) {
    items.push({ name: '查看视频', color: '#3B82F6' })
  }
  return items
})

// 点击预览按钮，打开操作菜单
const handlePreview = () => {
  showActionSheet.value = true
}

// 处理操作菜单选项点击
const handleActionClick = (evt: any) => {
  if (evt.item.name === '预览PDF文件') {
    // 打开PDF预览
    const pdfUrl = task.value.solution?.file
    uni.downloadFile({
      url: pdfUrl,
      success: function (res) {
        const filePath = res.tempFilePath
        uni.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log('打开文档成功')
          },
          fail: function () {
            uni.showToast({ title: 'PDF文件打开失败', icon: 'none' })
          },
        })
      },
      fail: function () {
        uni.showToast({ title: 'PDF文件下载失败', icon: 'none' })
      },
    })
  } else if (evt.item.name === '查看视频') {
    // 跳转到视频播放页面或直接播放视频
    const videoUrl = task.value.solution?.video
    uni.navigateTo({
      url: `/pages/common/video-player?url=${encodeURIComponent(videoUrl)}`,
    })
  }
}

const formData = ref<InspectHandleParams>({})

const formRef = ref<any>(null)
const editable = ref<boolean>(false)

const titleMap: Record<string, string> = {
  handle: '任务处理',
  view: '任务详情',
}

onLoad(async (options?: Record<string, string>) => {
  pageTitle.value = titleMap[options!.action] || '任务详情'
  showBottomActions.value = options!.action !== 'view'
  editable.value = options!.action !== 'view'

  const orderCode = options?.orderCode
  if (orderCode) {
    try {
      const data = await getInspectionWorkOrder({ orderCode })
      if (data) {
        task.value = data
        formData.value = {
          remark: data.remark || '',
          orderCode: data.orderCode,
        }

        if (Array.isArray(data.handleItems) && data.handleItems.length === 0) {
          formData.value.handleCheckItems =
            data.configItems?.map(item => ({ ...item, resultContent: undefined })) || []
        } else {
          formData.value.handleCheckItems =
            data.handleItems?.map(item => {
              if (item.resultType === 'image') {
                return {
                  ...item,
                  resultContent: item
                    .resultContent!.split(',')
                    .map((ele: string) => ({ url: ele.trim() })),
                }
              }
              return item
            }) || []
        }
      } else {
        task.value = {} as InspectionWorkOrder
        formData.value = {
          remark: '',
          orderCode,
          handleCheckItems: [],
        }
        toast.error('未获取到任务数据')
      }
    } catch (error) {
      toast.error('加载任务详情失败')
    }
  }

})

const message = useMessage()
const { eventEmit } = useChannel()

// 辅助函数：获取最新任务数据、发送事件并返回
const emitUpdateAndNavigateBack = async (customMessage?: string) => {
  eventEmit('taskUpdated', {
    orderCode: task.value.orderCode,
    updatedTask: task.value,
  })
  toast.success(customMessage || '操作成功')
  uni.navigateBack()
}

const doHandletask = async () => {
  if (isHandling.value) return
  isHandling.value = true
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      await message.confirm({
        title: '提交',
        msg: '确认提交任务处理结果吗',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      })
      const params = {
        orderCode: formData.value.orderCode,
        remark: formData.value.remark,
        handleCheckItems:
          formData.value.handleCheckItems?.map(item => {
            if (item.resultType === 'image' && Array.isArray(item.resultContent)) {
              return {
                ...item,
                resultContent: item.resultContent.map((file: any) => file.url || file).join(','),
              }
            }
            return item
          }) || [],
      }
      await handleInspectionWorkOrder(params)
      await emitUpdateAndNavigateBack('提交成功')
    }
  } catch (error) {
    // 错误处理逻辑，例如 toast.error('提交失败')
  } finally {
    isHandling.value = false
  }
}
</script>

<template>
  <view class="deal-page">
    <Navbar :title="pageTitle" :show-back="true" :safeAreaInsetTop="true" :placeholder="true">
      <template #right>
        <view class="preview-button f-c-c px-2" @click="handlePreview">
          <wd-icon name="view" size="20px" color="#333" />
        </view>
      </template>
    </Navbar>

    <StationInfo :station="task" />

    <TaskInfo :task="task" />
    <view>
      <view class="m-3 p-4 bg-white rounded-lg shadow-sm">
      <view class="flex items-center mb-3">
        <view class="accent-bar bg-primary w-1 h-4 mr-2"></view>
        <text class="text-sm font-bold text-[#4B4B4B]">任务处理</text>
      </view>
        <WorkorderHandleForm v-model:form-data="formData" :editable="editable" ref="formRef" />
      </view>
    </view>

    <view v-if="showBottomActions" class="bottom-actions fixed-bottom">
      <view class="flex w-full">
        <wd-button
          :round="false"
          type="primary"
          @click="doHandletask"
          custom-class="w-full"
          :loading="isHandling"
        >
          提交
        </wd-button>
      </view>
    </view>

    <!-- 操作菜单 -->
    <wd-action-sheet v-model="showActionSheet" :actions="actionItems" @select="handleActionClick" />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "任务审核"
  }
}
</route>

<style scoped lang="scss">
.deal-page {
  padding-bottom: 80px;
  background-color: #f7f7f5;

  .segmented-container {
    --wot-segmented-item-bg-color: #fff;

    :deep(.wd-segmented__item.is-active) {
      background-color: $uni-color-primary;
      color: white;
    }

    :deep(.wd-segmented__item) {
      color: #91929e;
      min-width: none;
    }

    :deep(.wd-segmented) {
      width: 100%;
    }
  }

  .bottom-actions {
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
    background-color: #fff;
    padding: 12px;
    z-index: 10;
  }

  .hidden-tabs-nav {
    :deep(.wd-tabs__nav) {
      display: none !important;
    }
  }

  .preview-button {
    height: 100%;
    border-radius: 4px;
  }
}
</style>
