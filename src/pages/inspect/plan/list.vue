<script setup lang="ts">
import FilterPopup from './components/FilterPopup.vue'
import type { SegmentedOption } from 'wot-design-uni/components/wd-segmented/types'
import { nextTick, ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import SearchBar from '@/components/SearchBar.vue'
import { useUserStore } from '@/store'
import { getInspectionWorkOrderStatisticsPlan } from '@/api'
import type {
  InspectionPlanStatistics,
  InspectWorkOrderStatisticsParams,
} from '@/types/api/Inspect'
import { setNavigationBarTitle } from '@uni-helper/uni-promises'

const keyword = ref('')
const activeTab = ref<string>('')
const showFilterPopup = ref(false)
const currentFilters = ref({})
const userStore = useUserStore()
const userInfo = userStore.userInfo

const tabs = ref<SegmentedOption[]>([
  {
    value: 'COMMON',
    payload: {
      label: '户用',
      badge: 0,
    },
  },
  {
    value: 'CM',
    payload: {
      label: '工商业',
      badge: 0,
    },
  },
  {
    value: 'PUB_BUILD',
    payload: {
      label: '公共租赁',
      badge: 0,
    },
  },
])

const paging = ref<any>()
const orderList = ref<InspectionPlanStatistics[]>([])
const pageSize = ref(20)

const inspectionType = ref<string>()
onLoad((options: any) => {
  inspectionType.value = options.inspectionType
  const title = !inspectionType.value
    ? '巡检计划'
    : inspectionType.value === 'ANNUAL'
    ? '年度巡检'
    : '临时巡检'
  setNavigationBarTitle({ title })
})

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pageSize: number) => {
  const params: InspectWorkOrderStatisticsParams = {
    pageNum,
    pageSize,
  }

  // 合并筛选条件
  Object.assign(params, currentFilters.value)

  if(keyword.value) {
    params.planName = keyword.value
  }

  try {
    const res = await getInspectionWorkOrderStatisticsPlan({
      ...params,
      inspectionType: inspectionType.value,
      stationType: activeTab.value as string,
    })
    paging.value?.completeByTotal(res.content, res.totalElements)
  } catch {
    paging.value?.complete(false)
  }
}

const goToDetail = (plan: InspectionPlanStatistics) => {
  if(userInfo?.userType === 'haier') {
    uni.navigateTo({
      url: `/pages/inspect/plan/detail?planId=${plan.planId}&planName=${plan.planName}`,
    })
  } else if(userInfo?.userType === 'merchant') {
    uni.navigateTo({
      url: `/pages/inspect/task/list?planId=${plan.planId}`,
    })
  }
}

const handleApplyFilter = (filters: any) => {
  currentFilters.value = filters
  showFilterPopup.value = false
  paging.value?.reload()
}

watch(activeTab, async () => {
  if (paging.value) {
    orderList.value = []
    await nextTick()
  }
  paging.value?.reload()
})
</script>

<template>
  <view class="work-plan-list-page">
    <!-- 搜索区域 -->
    <SearchBar
      v-model="keyword"
      :show-filter="true"
      @search="onSearch"
      @filter="showFilterPopup = true"
      placeholder="请输入巡检计划名称"
    />

    <!-- Tab 切换 -->
    <view class="segmented-container">
      <wd-segmented v-model:value="activeTab" :options="tabs">
        <template #label="{ option }">
          <view class="segmented-item-content">
            <text>{{ option.payload.label }}</text>
            <wd-badge
              v-if="option.payload.badge"
              :model-value="option.payload.badge"
              custom-class="segmented-item-badge"
              type="danger"
            />
          </view>
        </template>
      </wd-segmented>
    </view>

    <!-- 工单列表 -->
    <z-paging
      ref="paging"
      v-model="orderList"
      class="plan-list-paging"
      :fixed="false"
      @query="queryList"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
    >
      <view class="plan-list-content">
        <view
          v-for="plan in orderList"
          :key="plan.planId"
          class="plan-card"
          @click="goToDetail(plan)"
        >
          <view class="card-header">
            <text class="title">{{ plan.planName }}</text>
            <!-- <wd-tag :type="getStatusTagType(plan.status)" plain>{{ plan.statusText }}</wd-tag> -->
          </view>
          <view class="card-meta">
            <!-- <text>{{ plan.opName }}</text> -->
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">开始时间</text>
              <text class="value">{{ plan.startDate }}</text>
            </view>
            <view class="detail-item">
              <text class="label">结束时间</text>
              <text class="value">{{ plan.endDate }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站数量</text>
              <text class="value">{{ plan.stationCount }}</text>
            </view>
            <view class="detail-item">
              <text class="label">已巡检</text>
              <text class="value">{{ plan.inspectedCount }}</text>
            </view>
            <view class="detail-item">
              <text class="label">巡检进度</text>
              <text class="value flex-1">
                <wd-progress :percentage="plan.progress" custom-class="w-full px-1" />
              </text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <FilterPopup
      v-model:modelValue="showFilterPopup"
      @apply="handleApplyFilter"
      :stationType="activeTab"
      :inspectionType="inspectionType"
      :showStationCondition="false"
    />
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "巡检计划"
  }
}
</route>

<style scoped lang="scss">
.work-plan-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .segmented-container {
    padding: 8px 12px;

    --wot-segmented-item-bg-color: #fff;

    :deep(.wd-segmented__item.is-active) {
      background-color: $uni-color-primary;
      color: white;
    }

    :deep(.wd-segmented__item) {
      color: #91929e;
      min-width: none;
    }

    :deep(.wd-segmented) {
      width: 100%;
    }
  }

  .segmented-item-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .segmented-item-badge {
      position: absolute;
      top: 2px;
      right: -5px;
      transform: translate(50%, -50%) scale(0.8);
    }
  }

  .plan-list-paging {
    flex: 1;
  }

  .plan-list-content {
    padding: 8px 12px;
  }

  .plan-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-meta {
      font-size: 12px;
      color: #999;
      margin-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }

    .card-body {
      .detail-item {
        display: flex;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 4px;

        .label {
          color: #666;
          margin-right: 8px;
          flex-shrink: 0;
          width: 70px;
        }

        .value {
          color: #333;
          word-break: break-all;
        }
      }
    }

    &:last-child {
      margin-bottom: 0px;
    }
  }
}
</style>
