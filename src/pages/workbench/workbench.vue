<script lang="ts" setup>
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { ref, computed } from 'vue'

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

interface MenuItem {
  name: string
  icon: string
  path?: string
}

const workOrderItems = computed<MenuItem[]>(() => {
  const items: MenuItem[] = []

  if (userInfo.value?.userType === 'haier') {
    items.push(
      {
        name: '待审核',
        icon: '/static/workbench/approve.webp',
        path: '/pages/work-order/list/multi?type=approve',
      },
      {
        name: '待处理',
        icon: '/static/workbench/handle.webp',
        path: '/pages/work-order/list/multi?type=handle',
      },
    )
  }
  else if (userInfo.value?.isProvider) {
    items.push(
      {
        name: '待指派',
        icon: '/static/workbench/approve.webp',
        path: '/pages/work-order/list/single?type=assign',
      },
      {
        name: '待处理',
        icon: '/static/workbench/handle.webp',
        path: '/pages/work-order/list/multi?type=handle',
      },
    )
  }
  else {
    items.push(
      {
        name: '待处理',
        icon: '/static/workbench/handle.webp',
        path: '/pages/work-order/list/multi?type=handle',
      },
      {
        name: '已逾期',
        icon: '/static/workbench/approve.webp',
        path: '/pages/work-order/list/single?type=overtime',
      },
    )
  }

  items.push(
    {
      name: '全部',
      icon: '/static/workbench/all.webp',
      path: '/pages/work-order/list/single?type=all',
    },
    {
      name: '提报工单',
      icon: '/static/workbench/add.webp',
      path: '/pages/work-order/list/single?type=my',
    },
  )

  return items
})

const inspectionPlanItems = ref([
  { name: '年度巡检', icon: '/static/workbench/annual.webp', path: '/pages/inspect/plan/list?inspectionType=ANNUAL' },
  { name: '临时巡检', icon: '/static/workbench/temp.webp', path: '/pages/inspect/plan/list?inspectionType=TEMPORARY' },
  { name: '巡检任务', icon: '/static/workbench/task.webp', path: '/pages/inspect/task/list' },
])

const commonFunctionItems = ref([
  { name: '电站中心', icon: '/static/home/<USER>', path: '/pages/station/index' },
  { name: '方案库', icon: '/static/home/<USER>', path: '/pages/solution/list' },
  { name: '租金查询', icon: '/static/home/<USER>', path: '/pages/rent/index' },
  { name: '智能客服', icon: '/static/home/<USER>', path: '/pages/service/index' },
  { name: '培训中心', icon: '/static/home/<USER>' },
  { name: '消息管理', icon: '/static/home/<USER>', path: '/pages/message/list' },
])

function handleMenuItemClick(item: MenuItem) {
  if (item.path) {
    uni.navigateTo({
      url: item.path,
    })
  }
}
</script>

<template>
  <view class="workbench-page bg-gray-100 h-full box-border p-4">
    <view class="card bg-white rounded-lg shadow-md mb-4 p-4">
      <text class="text-lg font-semibold mb-3 block">我的工单</text>
      <view class="grid grid-cols-4 gap-x-4 gap-y-6 text-center">
        <view
          v-for="item in workOrderItems"
          :key="item.name"
          class="menu-item flex flex-col items-center active:opacity-80"
          @click="handleMenuItemClick(item)"
        >
          <view class="icon-wrapper w-14 h-14 mb-1 flex items-center justify-center">
            <image :src="item.icon" class="w-10 h-10" mode="aspectFit" />
          </view>
          <text class="text-xs text-gray-600">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <view class="card bg-white rounded-lg shadow-md mb-4 p-4">
      <text class="text-lg font-semibold mb-3 block">巡检计划</text>
      <view class="grid grid-cols-4 gap-x-4 gap-y-6 text-center">
        <view
          v-for="item in inspectionPlanItems"
          :key="item.name"
          class="menu-item flex flex-col items-center active:opacity-80"
          @click="handleMenuItemClick(item)"
        >
          <view class="icon-wrapper w-14 h-14 mb-1 flex items-center justify-center">
            <image :src="item.icon" class="w-10 h-10" mode="aspectFit" />
          </view>
          <text class="text-xs text-gray-600">{{ item.name }}</text>
        </view>
      </view>
    </view>

    <view class="card bg-white rounded-lg shadow-md mb-4 p-4">
      <text class="text-lg font-semibold mb-3 block">常用功能</text>
      <view class="grid grid-cols-4 gap-x-4 gap-y-6 text-center">
        <view
          v-for="item in commonFunctionItems"
          :key="item.name"
          class="menu-item flex flex-col items-center active:opacity-80"
          @click="handleMenuItemClick(item)"
        >
          <view class="icon-wrapper w-14 h-14 mb-1 flex items-center justify-center">
            <image :src="item.icon" class="w-10 h-10" mode="aspectFit" />
          </view>
          <text class="text-xs text-gray-600">{{ item.name }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "工作台"
  }
}
</route>

<style scoped></style>
