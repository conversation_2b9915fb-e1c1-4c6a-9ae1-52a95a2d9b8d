<script setup lang="ts">
import { ref, watch } from 'vue'
import { useToast } from 'wot-design-uni'
import { updateInverterMpptInfo } from '@/api/station'

interface MpptInfo {
  name: string
  total: number
  pv: Array<{
    name: string
    total: number
  }>
}

interface Props {
  modelValue: boolean
  mpptInfoList: MpptInfo[]
  inverterId: string | number | undefined
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'save'])

const toast = useToast()
const editingMpptList = ref<MpptInfo[]>([])

const showPopup = ref(props.modelValue)

watch(
  () => props.modelValue,
  (newValue) => {
    showPopup.value = newValue
    if (newValue) {
      if (props.mpptInfoList.length > 0) {
        editingMpptList.value = JSON.parse(JSON.stringify(props.mpptInfoList))
      } else {
        editingMpptList.value = [
          {
            name: 'mppt1',
            total: 0,
            pv: [{ name: 'pv1', total: 0 }],
          },
        ]
      }
    }
  },
)

watch(showPopup, (newValue) => {
  if (!newValue) {
    emit('update:modelValue', false)
  }
})

function addMppt() {
  const newMppt: MpptInfo = {
    name: `mppt${editingMpptList.value.length + 1}`,
    total: 0,
    pv: [{ name: 'pv1', total: 0 }],
  }
  editingMpptList.value.push(newMppt)
}

function removeMppt(index: number) {
  editingMpptList.value.splice(index, 1)
}

function addPv(mpptIndex: number) {
  const mppt = editingMpptList.value[mpptIndex]
  const existingNumbers = mppt.pv.map((pv) => {
    const match = pv.name.match(/pv(\d+)/)
    return match ? parseInt(match[1]) : 0
  })
  const nextNumber = Math.max(...existingNumbers, 0) + 1

  const newPv = {
    name: `pv${nextNumber}`,
    total: 0,
  }
  mppt.pv.push(newPv)
  updateMpptTotal(mpptIndex)
}

function removePv(mpptIndex: number, pvIndex: number) {
  editingMpptList.value[mpptIndex].pv.splice(pvIndex, 1)
  updateMpptTotal(mpptIndex)
}

function updateMpptTotal(mpptIndex: number) {
  const mppt = editingMpptList.value[mpptIndex]
  mppt.total = mppt.pv.reduce((sum, pv) => sum + pv.total, 0)
}

async function saveMpptInfo() {
  if (!props.inverterId) {
    toast.error('逆变器ID不存在')
    return
  }

  try {
    const mpptInfoStr = JSON.stringify(editingMpptList.value)
    await updateInverterMpptInfo({
      inverterId: props.inverterId,
      mpptInfo: mpptInfoStr,
    })

    toast.success('保存成功')
    emit('save', JSON.parse(JSON.stringify(editingMpptList.value)))
    cancelEdit()
  } catch (error) {
    toast.error('保存失败，请重试')
  }
}

function cancelEdit() {
  emit('update:modelValue', false)
}
</script>

<template>
  <wd-popup v-model="showPopup" position="bottom" :safe-area-inset-bottom="true" @close="cancelEdit">
    <view class="mppt-editor">
      <view class="mppt-editor__header">
        <text class="mppt-editor__title">
          {{ mpptInfoList.length > 0 ? '编辑MPPT信息' : '新增MPPT信息' }}
        </text>
        <wd-button type="text" :round="false" @click="cancelEdit">取消</wd-button>
      </view>
      <view class="mppt-editor__content">
        <scroll-view scroll-y class="mppt-editor__scroll">
          <view
            v-for="(mppt, mpptIndex) in editingMpptList"
            :key="mpptIndex"
            class="edit-mppt-item"
          >
            <view class="edit-mppt-header">
              <wd-input v-model="mppt.name" placeholder="MPPT名称" class="mppt-name-input" />
              <text class="mppt-total-text">总计: {{ mppt.total }}</text>
              <wd-button
                type="error"
                size="small"
                :round="false"
                @click="removeMppt(mpptIndex)"
                :disabled="editingMpptList.length <= 1"
              >
                删除
              </wd-button>
            </view>

            <view class="pv-edit-list">
              <view v-for="(pv, pvIndex) in mppt.pv" :key="pvIndex" class="pv-edit-item">
                <wd-input v-model="pv.name" placeholder="PV名称" class="pv-name-input" />
                <wd-input-number
                  v-model="pv.total"
                  :min="0"
                  :max="100"
                  @change="updateMpptTotal(mpptIndex)"
                  class="pv-total-input"
                />
                <wd-button
                  type="error"
                  size="small"
                  :round="false"
                  @click="removePv(mpptIndex, pvIndex)"
                  :disabled="mppt.pv.length <= 1"
                >
                  删除
                </wd-button>
              </view>

              <wd-button
                type="primary"
                size="small"
                :round="false"
                @click="addPv(mpptIndex)"
                class="add-pv-btn"
              >
                + 添加PV组串
              </wd-button>
            </view>
          </view>

          <wd-button type="primary" :round="false" @click="addMppt" class="add-mppt-btn">
            + 添加MPPT
          </wd-button>
        </scroll-view>
      </view>
      <view class="mppt-editor__footer">
        <wd-button type="primary" block :round="false" @click="saveMpptInfo">保存</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style scoped lang="scss">
.mppt-editor {
  padding: 20px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    flex-shrink: 0;
  }

  &__title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  &__content {
    flex: 1;
    overflow: hidden;
  }

  &__scroll {
    height: 100%;
    max-height: 50vh;
  }

  &__footer {
    padding-top: 20px;
    flex-shrink: 0;
  }
}

.edit-mppt-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.edit-mppt-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;

  .mppt-name-input {
    flex: 1;
  }

  .mppt-total-text {
    color: #606266;
    font-size: 14px;
    white-space: nowrap;
  }
}

.pv-edit-list {
  .pv-edit-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;

    .pv-name-input {
      flex: 1;
    }

    .pv-total-input {
      width: 100px;
    }
  }

  .add-pv-btn {
    margin-top: 10px;
    width: 100%;
  }
}

.add-mppt-btn {
  margin-top: 20px;
  width: 100%;
}
</style>
