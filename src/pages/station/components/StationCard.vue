<script lang="ts" setup>
import type { Station } from '@/types/api/Station'
import { navigateTo } from '@uni-helper/uni-promises'
import { useMapNavigation } from '@/composables/useMapNavigation'
const { openMapNavigation } = useMapNavigation()

const props = withDefaults(
  defineProps<{
    item: Station
    showNavigate?: boolean
  }>(),
  {
    showNavigate: true,
  },
)

const emit = defineEmits<{
  (e: 'click', item: Station): void
}>()

const handleClick = (item: Station) => {
  emit('click', item)
}
</script>

<template>
  <view class="station-card" @click="handleClick(item)">
    <view class="card-header">
      <text class="title">{{ item.stationCode }}</text>
      <view
        v-if="item.longitude && item.latitude && props.showNavigate"
        class="navigate-button"
        @click.stop="openMapNavigation(item)"
      >
        <text class="navigate-button__text">导航</text>
        <image src="/static/common/location.webp" class="navigate-button__icon" />
      </view>
    </view>
    <view class="card-meta">
      <text>{{ item.createdAt }}</text>
    </view>
    <view class="card-body">
      <view class="detail-item">
        <text class="label">电站名称</text>
        <text class="value">{{ item.name }}</text>
      </view>
      <view class="detail-item">
        <text class="label">电站地址</text>
        <text class="value">{{ item.address }}</text>
      </view>
      <view class="detail-item">
        <text class="label">联系方式</text>
        <text class="value">{{ item.phone }}</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.station-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }

    .navigate-button {
      background-color: rgba(55, 172, 254, 0.1);
      width: 59px;
      height: 30px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 4px;
      border: none;
      line-height: 1;
      gap: 4px;
      margin: 0px;

      &__text {
        color: $uni-color-primary;
        line-height: 12px;
        font-size: 14px;
      }

      &__icon {
        width: 25px;
        height: 25px;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }

  .card-meta {
    font-size: 12px;
    color: #999;
    margin-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }

  .card-body {
    .detail-item {
      display: flex;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 4px;

      .label {
        color: #666;
        margin-right: 8px;
        flex-shrink: 0;
        width: 70px;
      }

      .value {
        color: #333;
        word-break: break-all;
        flex: 1;
      }
    }
  }

  &:last-child {
    margin-bottom: 0px;
  }
}
</style>
