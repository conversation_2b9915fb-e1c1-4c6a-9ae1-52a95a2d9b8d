<script setup lang="ts">
import { ref } from 'vue'
import { watchOnce } from '@vueuse/core'
import { onLoad } from '@dcloudio/uni-app'
import type { Inverter } from '@/types/api/Station'
import { useChannel } from '@/composables/useChannel'
import InverterDetailCard from './components/InverterDetailCard.vue'

const inverterSn = ref('')
const inverterInfo = ref<Inverter>({})

const { eventChannel } = useChannel()

// 处理接收到的inverter数据
function handleInverterData(data: { inverter: Inverter }) {
  console.log('Received inverter data:', data)
  if (data?.inverter) {
    inverterInfo.value = data.inverter
  }
}

onLoad((options) => {
  if (options?.inverterSn) {
    inverterSn.value = options.inverterSn
  }
})

// 监听EventChannel的变化，当EventChannel可用时设置监听器（只执行一次）
watchOnce(eventChannel, (newEventChannel) => {
  if (newEventChannel) {
    newEventChannel.on('receiveInverterData', handleInverterData)
  }
})
</script>

<template>
  <view class="inverter-page">
    <InverterDetailCard v-if="inverterInfo.inverterSn" :inverter="inverterInfo" />
    <view v-else class="loading-state">
      <text>加载中...</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.inverter-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}
</style>


