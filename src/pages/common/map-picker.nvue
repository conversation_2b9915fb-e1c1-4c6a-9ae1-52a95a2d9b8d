<script setup lang="ts">
import { ref, getCurrentInstance, onMounted } from 'vue'
import { onLoad, onReady } from '@dcloudio/uni-app'
import { getLocation as uniGetLocation, showToast, navigateBack } from '@uni-helper/uni-promises'
import { useChannel } from '@/composables/useChannel'
import { useDebounceFn } from '@vueuse/core'

const { eventEmit } = useChannel()

const mapCtx = ref<UniApp.MapContext | null>(null)
const initialLatitude = ref(39.909) // 默认北京
const initialLongitude = ref(116.39742)
const currentLatitude = ref(initialLatitude.value)
const currentLongitude = ref(initialLongitude.value)
const {
  windowHeight,
  windowWidth,
  safeAreaInsets
} = uni.getWindowInfo();

onLoad(async options => {

  if (options?.latitude && options?.longitude) {
    initialLatitude.value = parseFloat(options.latitude)
    initialLongitude.value = parseFloat(options.longitude)
    currentLatitude.value = initialLatitude.value
    currentLongitude.value = initialLongitude.value
  } else {
    try {
      const res = await uniGetLocation({ type: 'gcj02' })
      initialLatitude.value = res.latitude
      initialLongitude.value = res.longitude
      currentLatitude.value = initialLatitude.value
      currentLongitude.value = initialLongitude.value
    } catch (e) {
      console.error('Failed to get current location for map picker', e)
      showToast({ title: '获取当前位置失败', icon: 'none' })
    }
  }
})

function onMapReady() {
  console.log('onMapReady')
}

onReady(() => {
  const instance = getCurrentInstance()
  mapCtx.value = uni.createMapContext('locationMap', instance.proxy)
})

const debouncedUpdateCenterLocation = useDebounceFn(() => {
  if (mapCtx.value) {
    mapCtx.value!.getCenterLocation({
      success: res => {
        currentLatitude.value = res.latitude
        currentLongitude.value = res.longitude
        console.log(res)
      },
      fail: err => {
        console.error('Failed to get center location:', err)
      },
    })
  }
}, 300)

function confirmLocation() {
  eventEmit('acceptDataFromMapPicker', {
    latitude: currentLatitude.value,
    longitude: currentLongitude.value,
  })
  navigateBack()
}
</script>

<template>
  <view>
    <map
      id="locationMap"
      class="map-view"
      :latitude="initialLatitude"
      :longitude="initialLongitude"
      :scale="16"
      show-location
      @regionchange="debouncedUpdateCenterLocation"
      @ready="onMapReady"
      :style="{height: windowHeight,width: windowWidth}"
    >
      <!-- <cover-view class="center-marker"> -->
        <image class="marker-image" :style="{left: windowWidth/2,top: windowHeight/2}" src="/static/common/location.webp" />
      <!-- </cover-view> -->
    </map>
    <view class="actions-bar" :style="{paddingBottom: safeAreaInsets.bottom + 15}">
      <button type="primary" block @click="confirmLocation">确定</button>
    </view>
  </view>
</template>

<style scoped>
.map-view {
  position: relative;
}

.marker-image {
  width: 32px;
  height: 32px;
  position: absolute;
  margin-left: -16px;
  margin-top: -32px;
}

.actions-bar {
  position: fixed;
  bottom: 0px;
  width: 750rpx;
  padding: 15px;
  background-color: #ffffff;
  border-top-width: 1px;
  border-top-color: #ebedf0;
  border-top-style: solid;
  /* padding-bottom: env(safe-area-inset-bottom) */
}
</style>
