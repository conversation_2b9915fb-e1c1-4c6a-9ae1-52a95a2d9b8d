<template>
  <view class="card m-3 p-4 bg-white rounded-lg shadow-sm">
    <view class="card-title flex items-center mb-3">
      <view class="accent-bar bg-primary w-1 h-4 mr-2"></view>
      <text class="text-sm font-bold text-[#4B4B4B]">故障信息</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障名称</text>
      <text class="value">{{ faultInfo?.faultName || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障等级</text>
      <text class="value">{{ faultLevelLabel }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障描述</text>
      <text class="value">{{ faultInfo?.faultDetails || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">故障照片</text>
      <view class="value flex gap-4 flex-wrap">
        <wd-img
          v-for="(img, index) in photos"
          :key="index"
          :src="img"
          custom-class="w-25 h-25 rounded-md bg-gray-200"
          @click="previewImages(index)"
        />
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useDictStore } from '@/store/modules/dict'

const props = defineProps({
  faultInfo: {
    type: Object,
    default: () => ({}),
  },
  photos: {
    type: Array,
    default: () => [],
  },
})

const dictStore = useDictStore()

const faultLevelLabel = computed(() => {
  return dictStore.getDictLabel('fault_level', props.faultInfo?.faultLevel)
})

const previewImages = index => {
  uni.previewImage({
    urls: props.photos,
    current: props.photos[index],
  })
}
</script>

<style scoped>
.detail-row {
  display: flex;
  margin-bottom: 10px;
}

.label {
  color: #999;
  font-size: 14px;
  width: 88px;
}

.value {
  color: #333;
  font-size: 14px;
  flex: 1;
}
</style>
