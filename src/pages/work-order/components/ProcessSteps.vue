<template>
  <view v-if="processes && processes.length > 0" class="p-2">
    <wd-steps :active="processes.length" vertical dot>
      <wd-step v-for="(process, pIndex) in processes" :key="pIndex">
        <template #title>
          <view class="font-bold text-sm">{{ process.processName || '未命名流程' }} - {{ process.createdBy || '未知用户' }}</view>
        </template>
        <template #description>
          <view class="text-xs text-gray-500 mb-2">{{ process.createdAt || '' }}</view>
          <view
            v-if="process.handleCheckItemsJson && parseCheckItems(process.handleCheckItemsJson).length > 0"
            class="check-items-container bg-gray-50 p-3 rounded-md shadow-sm"
          >
            <view class="text-xs font-semibold mb-1 text-gray-700">处理检查项：</view>
            <view
              v-for="(item, cIndex) in parseCheckItems(process.handleCheckItemsJson)"
              :key="cIndex"
              class="check-item mb-1.5"
            >
              <view class="text-xs text-gray-600">
                <text class="font-medium text-gray-800">{{ item.checkItem }}: </text>
                <template v-if="item.resultType === 'text' || item.resultType === 'select'">
                  {{ item.resultContent || '-' }}
                </template>
                <template
                  v-else-if="
                    item.resultType === 'image' &&
                    Array.isArray(item.resultContent) &&
                    item.resultContent.length > 0
                  "
                >
                  <view class="image-list flex flex-wrap gap-2 mt-1">
                    <wd-img
                      v-for="(imgUrl, imgIdx) in (item.resultContent as string[])"
                      :key="imgIdx"
                      :src="imgUrl"
                      width="60px"
                      height="60px"
                      mode="aspectFill"
                      custom-class="rounded border border-gray-200"
                      @click="previewImage(item.resultContent as string[], imgIdx)"
                    />
                  </view>
                </template>
                <template v-else>-</template>
              </view>
            </view>
          </view>
        </template>
      </wd-step>
    </wd-steps>
  </view>
  <view v-else class="text-gray-500 p-4 text-center">暂无工单流程信息。</view>
</template>

<script setup lang="ts">
interface Process {
  processName?: string
  createdBy?: string
  createdAt?: string
  handleCheckItemsJson?: string
  id?: number
  processDesc?: string
  workOrderId?: number
  [key: string]: any
}

defineProps({
  processes: {
    type: Array as () => Process[],
    default: () => []
  }
})

const parseCheckItems = (jsonString?: string) => {
  if (!jsonString) return []
  try {
    const items: Array<{
      checkItem: string
      resultType: 'text' | 'select' | 'image'
      resultContent: string
    }> = JSON.parse(jsonString)
    return items.map(item => {
      let parsedResultContent: string | string[]
      if (item.resultType === 'image' && typeof item.resultContent === 'string') {
        parsedResultContent = item.resultContent
          .split(',')
          .map(s => s.trim())
          .filter(s => s)
      } else {
        parsedResultContent = item.resultContent
      }
      return {
        checkItem: item.checkItem,
        resultType: item.resultType,
        resultContent: parsedResultContent,
      }
    })
  } catch (e) {
    console.error('Failed to parse handleCheckItemsJson:', e)
    return []
  }
}

const previewImage = (imageUrls: string[], currentIndex: number) => {
  uni.previewImage({
    urls: imageUrls,
    current: currentIndex,
  })
}
</script>

<style scoped>
.check-items-container {
  margin-top: 4px;
}
</style>
