<script setup lang="ts">
import { ref } from 'vue'

const monthOptions = ref([
  { text: '一月', value: 1 },
  { text: '二月', value: 2 },
  { text: '三月', value: 3 },
  { text: '四月', value: 4 },
  { text: '五月', value: 5 },
  { text: '六月', value: 6 },
  { text: '七月', value: 7 },
  { text: '八月', value: 8 },
  { text: '九月', value: 9 },
  { text: '十月', value: 10 },
  { text: '十一月', value: 11 },
  { text: '十二月', value: 12 },
])
const selectedMonth = ref<number | undefined>()
const showPicker = ref(false)

const onConfirmMonth = ({ selectedValue }: { selectedValue: any }) => {
  selectedMonth.value = selectedValue[0]
  showPicker.value = false
}
</script>

<template>
  <view class="rent-detail">
    <view class="info-section">
      <view class="info-item">
        <text class="info-item__label">电站名称</text>
        <text class="info-item__value">李军(02000006009)</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">联系方式</text>
        <text class="info-item__value info-item__value--link">15728221333</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">电站地址</text>
        <text class="info-item__value">龙源河南林州住在100MW光伏电站</text>
      </view>
      <view class="info-item info-item--vertical">
        <text class="info-item__label">电站照片</text>
        <view class="info-item__image-container">
          <image
            class="info-item__image"
            src="https://image-resource.mastergo.com/145686385811298/145686385811300/4e8c2650c4ba0e021af2c9e4ccaff51a.png"
            mode="aspectFill"
          />
        </view>
      </view>
      <view class="info-item">
        <text class="info-item__label">请选择月份</text>
        <wd-picker
          v-model:show="selectedMonth"
          :columns="monthOptions"
          label-key="text"
          value-key="value"
          @confirm="onConfirmMonth"
          custom-class="info-item__value info-item__value--placeholder"
          @cancel="showPicker = false"
        />
      </view>
      <view class="info-item">
        <text class="info-item__label">项目公司</text>
        <text class="info-item__value">青岛海斯顿新能源有限公司</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">新发电户号</text>
        <text class="info-item__value">3701149284753</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">发电量</text>
        <text class="info-item__value">13213/KWh</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">电费单价</text>
        <text class="info-item__value">0.66元/KWh</text>
      </view>
      <view class="info-item">
        <text class="info-item__label">应收电费</text>
        <text class="info-item__value">9868.5元</text>
        <view class="status-badge">
          <text class="status-badge__text">已到账</text>
        </view>
      </view>
      <view class="info-item">
        <text class="info-item__label">银行卡号</text>
        <text class="info-item__value">131323132132132132</text>
      </view>
      <view class="info-item info-item--last">
        <text class="info-item__label">开户行</text>
        <text class="info-item__value">中国农业银行</text>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "租金详情"
  }
}
</route>

<style scoped lang="scss">
.rent-detail {
  background-color: #ffffff;
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom) + 95px);
}

.info-section {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px;
  border-bottom: 1px solid #f2f2f2;
  font-size: 15px;

  &--vertical {
    align-items: flex-start;
    padding-top: 15px;
    padding-bottom: 15px;
  }

  &--last {
    border-bottom: none;
  }

  &__label {
    color: #91929e;
    flex-shrink: 0;
    margin-right: 10px;
    min-width: 70px;
  }

  &__value {
    color: #4b4b4b;
    text-align: right;
    flex-grow: 1;

    &--link {
      color: #409eff;
    }

    &--placeholder {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      border: 1px dashed #dee2e6;
      border-radius: 4px;
      padding: 8px 14px;
      height: 36px;
      box-sizing: border-box;
      width: 100%;
      max-width: calc(100% - 80px);
    }
  }

  &__image-container {
    width: 232px;
    height: 100px;
    border-radius: 12px;
    overflow: hidden;
  }

  &__image {
    width: 100%;
    height: 100%;
  }

  :deep(.wd-picker__cell) {
    padding: 0px;
  }
}

.status-badge {
  background-color: #f0f9eb;
  border: 1px dashed #67c23a;
  border-radius: 12px;
  padding: 2px 10px;
  margin-left: 10px;

  &__text {
    color: #67c23a;
    font-size: 12px;
  }
}

.text-gray {
  color: #91929e;
}
</style>
