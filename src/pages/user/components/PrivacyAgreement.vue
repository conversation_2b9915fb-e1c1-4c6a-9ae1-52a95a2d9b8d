<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'agree', 'disagree'])

function handleAgree() {
  emit('agree')
}

function handleDisagree() {
  emit('disagree')
}

function goToAgreement(type: 'service' | 'privacy') {
  uni.showToast({ title: `查看${type === 'service' ? '服务协议' : '隐私政策'}`, icon: 'none' })
}
</script>

<template>
  <wd-popup v-model="props.visible" :close-on-click-modal="false">
    <view class="privacy-dialog">
      <view class="privacy-dialog__title">隐私协议</view>
      <view class="privacy-dialog__content">
        <scroll-view class="content-scroll" scroll-y>
          <view class="content-text">
            <view class="paragraph">
              感谢您使用我们的应用！我们非常重视您的个人信息和隐私保护。为了更好地保障您的权益，在您使用我们的服务前，请您仔细阅读并了解
              <text class="link" @click="goToAgreement('service')">《服务协议》</text>和
              <text class="link" @click="goToAgreement('privacy')">《隐私政策》</text>
              的全部内容。
            </view>
            <view class="paragraph">
              这些协议和政策已经详细列明了我们收集、使用、存储和共享您个人信息的情况，以及您所享有的相关权利。其中包括但不限于：
            </view>
            <view class="paragraph-list">
              1. 我们可能收集的个人信息类型；
              2. 我们如何使用这些信息；
              3. 您如何管理自己的个人信息；
              4. 我们如何保护您的个人信息安全。
            </view>
            <view class="paragraph">
              您点击"同意"即表示您已阅读并同意上述协议和政策。如您不同意，很遗憾我们将无法为您提供完整的服务。
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="privacy-dialog__footer">
        <wd-button class="btn-disagree" type="info" plain @click="handleDisagree">不同意</wd-button>
        <wd-button class="btn-agree" type="primary" @click="handleAgree">同意</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.privacy-dialog {
  width: 80vw;
  height: 60vh;
  max-width: 560rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &__title {
    padding: 30rpx 0;
    text-align: center;
    font-size: 34rpx;
    font-weight: bold;
    border-bottom: 1rpx solid #eee;
  }

  &__content {
    padding: 30rpx;
    flex: 1;
    overflow: hidden;

    .content-scroll {
      height: 100%;

      .content-text {
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;

        .paragraph {
          margin-bottom: 20rpx;
        }

        .paragraph-list {
          margin-bottom: 20rpx;
          padding-left: 20rpx;
        }

        .link {
          color: #2887ff;
          display: inline;
        }
      }
    }
  }

  &__footer {
    display: flex;
    padding: 20rpx 30rpx 30rpx;

    .btn-disagree, .btn-agree {
      flex: 1;
      border-radius: 7.5px;
    }

    .btn-disagree {
      margin-right: 20rpx;
      border-color: #D0D3D5;
      min-width: unset;
    }

    .btn-agree {
      font-weight: 500;
      min-width: unset;
    }
  }
}
</style>
