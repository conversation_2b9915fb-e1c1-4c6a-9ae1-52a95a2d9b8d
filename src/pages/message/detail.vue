<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getMessageDetail, markMessageRead } from '@/api/message'
import type { Message } from '@/types/api/Message'
import { dayjs } from '@/plugins/dayjs'

const messageDetail = ref<Message | null>(null)
const messageId = ref<number | null>(null)

onLoad(async (options) => {
  if (options?.id) {
    const id = Number(options.id)
    messageId.value = id
    try {
      const detail = await getMessageDetail(id)
      messageDetail.value = detail
      if (detail.status !== 'READ') {
        await markMessageRead(id)
      }
    }
    catch (error) {
      uni.showToast({
        title: '加载失败',
        icon: 'none',
      })
    }
  }
  else {
    uni.showToast({
      title: '无效的消息ID',
      icon: 'none',
      complete: () => {
        uni.navigateBack()
      },
    })
  }
})

function formatTime(timeStr: string | undefined, format = 'YYYY-MM-DD HH:mm') {
  if (!timeStr)
    return ''
  return dayjs(timeStr).format(format)
}

function handleDownloadAttachment() {
  if (messageDetail.value && messageDetail.value.attachmentUrl) {
    uni.downloadFile({
      url: messageDetail.value.attachmentUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          uni.openDocument({
            filePath: res.tempFilePath,
            showMenu: true,
            success: () => {
              // console.log('打开文档成功')
            },
            fail: (err) => {
              uni.showToast({
                title: '打开附件失败',
                icon: 'none',
              })
              console.error('打开文档失败：', err)
            },
          })
        }
        else {
          uni.showToast({
            title: '下载附件失败',
            icon: 'none',
          })
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '下载附件失败',
          icon: 'none',
        })
        console.error('下载文件失败：', err)
      },
    })
  }
  else {
    uni.showToast({
      title: '附件链接无效',
      icon: 'none',
    })
  }
}
</script>

<template>
  <view class="message-detail-page">
    <template v-if="messageDetail">
      <view class="message-detail-page__header">
        <view class="message-detail-page__subject">
          {{ messageDetail.subject }}
        </view>
        <view class="message-detail-page__meta">
          <text>创建人：{{ messageDetail.createdByName }}</text>
          <text>发布时间：{{ formatTime(messageDetail.sendTime) }}</text>
        </view>
        <view class="message-detail-page__line" />
      </view>

      <view class="message-detail-page__content-wrapper">
        <view class="message-detail-page__html-content">
          <rich-text :nodes="messageDetail.content" />
        </view>
      </view>
    </template>
    <view v-else class="message-detail-page__loading-placeholder">
      <text>加载中...</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "通知详情"
  }
}
</route>

<style scoped lang="scss">
.message-detail-page {
  background-color: #ffffff;
  min-height: 100vh;
  padding: 16px;
}

.message-detail-page__header {
  margin-bottom: 16px;
}

.message-detail-page__subject {
  color: #2c3e50;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}

.message-detail-page__meta {
  color: #7f8c8d;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  margin-bottom: 16px;
}

.message-detail-page__line {
  background-color: #edf2f7;
  height: 1px;
}

.message-detail-page__content-wrapper {
  background-color: #ffffff;
}

.message-detail-page__html-content {
  color: #34495e;
  line-height: 1.6;
  word-break: break-all;
  font-size: 15px;

  // Rich text styles might need global styling if :deep doesn't work as expected
  // or if specific tags need styling.
  // For example:
  // :deep(p) {
  //   margin-bottom: 10px;
  // }
  // :deep(img) {
  //   max-width: 100%;
  //   height: auto;
  // }
}

.message-detail-page__loading-placeholder {
  color: #999;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>
