<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { getSolutionCategoryList, getSolutionPage } from '@/api/solution'
import type { Solution, SolutionPageParams } from '@/types/api/Solution'
import type { Option } from '@/types/common/index'
import type { PaginatedContent } from '@/service/types'
import { useDictStore } from '@/store/modules/dict'

const paging = ref<ZPagingInstance | null>(null)
const solutionList = ref<Solution[]>([])
const dictStore = useDictStore()

const tabs = computed(() => {
  return dictStore.getDictByType('solution_type')?.map(item => {
    return {
      title: item.label,
      value: item.value,
    }
  })
})

const activeTab = ref('inspection')

const queryParams = reactive<SolutionPageParams>({
  name: undefined,
  categoryId: undefined,
  pageNum: 1,
  pageSize: 10,
})

const fetchData = async (pageNo: number, pageSize: number) => {
  try {
    const params: SolutionPageParams = {
      ...queryParams,
      pageNum: pageNo,
      pageSize,
      solutionType: activeTab.value,
    }

    const paginatedResult: PaginatedContent<Solution> = await getSolutionPage(params)

    paging.value?.completeByTotal(paginatedResult.content, paginatedResult.totalElements)
  } catch (error) {
    console.error('Failed to fetch solution list:', error)
    paging.value?.complete(false)
  }
}

const onTabChange = (evt: { name: string | number }) => {
  activeTab.value = evt.name as string
  queryParams.categoryId = ''
  paging.value?.reload()
}

const categoryOptions = ref<Option[]>([])
const solutionTypeCategoryOptions = computed(() => {
  return [
    { label: '全部', value: '' },
    ...categoryOptions.value.filter(item => item.solutionType === activeTab.value),
  ]
})
const fetchCategory = () => {
  getSolutionCategoryList().then(res => {
    categoryOptions.value = res.map(item => {
      return {
        label: item.name!,
        value: item.id!,
        ...item,
      }
    })
  })
}

const handleSearch = () => {
  queryParams.pageNum = 1
  paging.value?.reload()
}

const goToDetail = (id?: number) => {
  if (!id) return
  uni.navigateTo({
    url: `/pages/solution/detail?id=${id}`,
  })
}

onMounted(() => {
  dictStore.fetchDict(['solution_type'])
  fetchCategory()
})
</script>

<template>
  <view class="solution-list-page">
    <SearchBar :show-filter="true" mode="light" v-model="queryParams.name" @search="handleSearch" placeholder="输入方案名称">
      <template #filter-trigger="slotProps">
        <wd-select-picker
          v-model="queryParams.categoryId!"
          use-default-slot
          :columns="solutionTypeCategoryOptions"
          type="radio"
          @confirm="handleSearch"
        >
          <view :class="slotProps.iconClasses">
            <text :class="slotProps.textClasses"></text>
          </view>
        </wd-select-picker>
      </template>
    </SearchBar>

    <wd-tabs v-model="activeTab" @change="onTabChange">
      <wd-tab v-for="(tab, index) in tabs" :key="index" :title="tab.title" :name="tab.value" />
    </wd-tabs>

    <z-paging
      ref="paging"
      v-model="solutionList"
      class="order-list-paging"
      :fixed="false"
      @query="fetchData"
      :default-page-size="20"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
    >
      <view class="list-content p-3 space-y-3">
        <view
          v-for="item in solutionList"
          :key="item.id"
          class="solution-card"
          @click="goToDetail(item.id)"
        >
          <view class="flex">
            <image src="/static/solution/profile-bg.webp" mode="aspectFill" class="solution-card__image" />
            <view class="flex-1">
              <text class="solution-card__title">{{ item.name }}</text>
              <text class="solution-card__description">
                {{ item.categoryName }}
              </text>
              <text class="solution-card__description">
                {{ item.createdAt }}
              </text>
              <!-- <view class="mt-1">
                <wd-tag :type="getTagType(item.categoryName)" custom-class="mr-1 text-xs" round>
                  {{ getSolutionTypeLabel(item.solutionType) }}
                </wd-tag>
              </view> -->
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "layout": "pageBg",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "方案库"
  }
}
</route>

<style scoped lang="scss">
.solution-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f3f4f6;
}

.solution-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 12px;

  &__image {
    width: 80px;
    height: 80px;
    border-radius: 6px;
    background-color: #e5e7eb;
    margin-right: 12px;
  }

  &__title {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: #1f2937;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__description {
    display: block;
    font-size: 14px;
    color: #6b7280;
    margin-top: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
