import { merchantInstance } from '@/service'

/**
 * @summary 获取省市区列表
 * @description 获取省市区列表
 */
export async function getRegionList(): Promise<Record<string, any>> {
  return await merchantInstance.get('/member/address/regionList.do')
}

/**
 * @summary 获取天气
 * @description 获取天气
 */
export async function getWeather(params: {
  lat: number
  lng: number
}): Promise<Record<string, any>> {
  return await merchantInstance.get('/lightOperationProvider/weatherByCoordinates.do', { params })
}
