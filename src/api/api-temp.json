{"swagger": "2.0", "info": {"description": "外部系统调用网关", "version": "1.0", "title": "日日顺乐农商户平台网关API接口", "contact": {"name": "rrsjk", "url": "http://www.rrsjk.com", "email": "<EMAIL>"}}, "host": "operation.xiaoxianglink.com", "basePath": "/hdsapi", "paths": {"/light/operation/exam/practice/complete": {"post": {"tags": ["光伏运维/考试练习功能管理"], "summary": "完成练习", "operationId": "completePracticeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "practiceId", "in": "query", "description": "练习ID", "required": false, "type": "integer", "format": "int64", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/practice/delete": {"post": {"tags": ["光伏运维/考试练习功能管理"], "summary": "删除练习记录", "operationId": "deletePracticeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "practiceId", "in": "query", "description": "练习ID", "required": false, "type": "integer", "format": "int64", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/practice/detail": {"get": {"tags": ["光伏运维/考试练习功能管理"], "summary": "获取练习详情", "operationId": "getPracticeDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "practiceId", "in": "query", "description": "练习ID", "required": false, "type": "integer", "format": "int64", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«PracticeResultVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/practice/generate": {"post": {"tags": ["光伏运维/考试练习功能管理"], "summary": "生成练习题目", "operationId": "generatePracticeQuestionsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/PracticeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«PracticeResultVO»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/practice/info": {"get": {"tags": ["光伏运维/考试练习功能管理"], "summary": "获取练习基本信息", "operationId": "getPracticeInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "practiceId", "in": "query", "description": "练习ID", "required": false, "type": "integer", "format": "int64", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«LightOperationPractice»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/practice/list": {"get": {"tags": ["光伏运维/考试练习功能管理"], "summary": "获取用户练习记录列表", "operationId": "getUserPracticesUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«List«LightOperationPractice»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/practice/updateProgress": {"post": {"tags": ["光伏运维/考试练习功能管理"], "summary": "更新练习答题进度", "operationId": "updatePracticeProgressUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"in": "body", "name": "answerData", "description": "答题数据，格式：{questionId: answer}", "required": false, "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, {"name": "practiceId", "in": "query", "description": "练习ID", "required": false, "type": "integer", "format": "int64", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "PracticeResultVO": {"type": "object", "properties": {"bankNames": {"type": "array", "description": "涉及的题库名称列表", "items": {"type": "string"}}, "multipleChoiceCount": {"type": "integer", "format": "int32", "description": "多选题数量"}, "practiceId": {"type": "string", "description": "练习ID"}, "questions": {"type": "array", "description": "题目列表", "items": {"$ref": "#/definitions/QuestionVO"}}, "singleChoiceCount": {"type": "integer", "format": "int32", "description": "单选题数量"}, "startTime": {"type": "integer", "format": "int64", "description": "练习开始时间戳"}, "totalCount": {"type": "integer", "format": "int32", "description": "总题数"}}, "title": "PracticeResultVO", "description": "练习结果展示对象"}, "PracticeRequest": {"type": "object", "required": ["bankIds"], "properties": {"bankIds": {"type": "array", "example": [1, 2, 3], "description": "题库ID列表", "items": {"type": "integer", "format": "int64"}}, "difficulties": {"type": "array", "example": [1, 2], "description": "难度过滤", "items": {"type": "integer", "format": "int32"}}, "questionTypes": {"type": "array", "example": ["SINGLE", "MULTIPLE"], "description": "题目类型过滤", "items": {"type": "string"}}, "randomOrder": {"type": "boolean", "example": true, "description": "是否随机排序"}}, "title": "PracticeRequest", "description": "练习题目请求DTO"}, "LightOperationPractice": {"type": "object", "properties": {"answerData": {"type": "string", "description": "答题数据"}, "answeredCount": {"type": "integer", "format": "int32", "description": "已答题数"}, "bankIds": {"type": "string", "description": "题库ID列表"}, "bankNames": {"type": "string", "description": "题库名称列表"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "createdBy": {"type": "string", "description": "创建人"}, "id": {"type": "integer", "format": "int64", "description": "主键ID"}, "lastUpdateTime": {"type": "string", "format": "date-time", "description": "最后更新时间"}, "multipleChoiceCount": {"type": "integer", "format": "int32", "description": "多选题数量"}, "questionData": {"type": "string", "description": "题目数据"}, "singleChoiceCount": {"type": "integer", "format": "int32", "description": "单选题数量"}, "startTime": {"type": "string", "format": "date-time", "description": "开始时间"}, "status": {"type": "string", "description": "练习状态"}, "totalCount": {"type": "integer", "format": "int32", "description": "总题数"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}, "updatedBy": {"type": "string", "description": "更新人"}, "userId": {"type": "string", "description": "用户ID"}, "userName": {"type": "string", "description": "用户姓名"}}, "title": "LightOperationPractice", "description": "练习记录实体"}}