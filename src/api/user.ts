import type { LoginPayload, LoginResponse } from '@/types/api/Auth'
import type {
  RegisterParams,
  SendRegisterSmsParams,
  SendUserForgetPasswordSmsParams,
  Staff,
  UserInfo,
  UserResetPasswordParams,
} from '@/types/api/User'
import { HDSBaseAuth, MerchantBaseAuth } from '@/constants'
import { authInstance, hdsInstance, merchantInstance } from '@/service'
import { ContentType } from '@/service/types'

export function login(data: LoginPayload) {
  let authorization = HDSBaseAuth
  data.grant_type = 'hds'
  data.scope = 'hds'
  if (data.userType === 'merchant') {
    authorization = MerchantBaseAuth
    data.grant_type = 'password'
    data.scope = 'pc'
  }
  delete data.userType
  return authInstance.post<LoginResponse>(`/oauth2/oauth/token`, data, {
    headers: {
      authorization,
      'Content-Type': ContentType.APPLICATION_X_WWW_FORM_URLENCODED,
    },
  })
}

/**
 * @summary 获取当前登录用户信息
 * @description 获取当前登录用户信息 (Swagger OperationId: getUserInfoUsingGET)
 * @returns Promise<ApiResponse<UserInfo>>
 */
export async function getAppUserInfo(userType: string): Promise<UserInfo> {
  const urlMap: Record<string, string> = {
    haier: '/light/operation/app/user/info',
    merchant: '/light/operation/app/user/info',
  }
  const path = urlMap[userType!]
  const client = userType === 'haier' ? hdsInstance : merchantInstance
  return client.get(path)
}

/**
 * @summary 发送忘记密码短信验证码
 * @description 验证手机号并发送忘记密码的验证码
 * @param data SendUserForgetPasswordSmsParams
 * @returns Promise<boolean>
 */
export function sendUserForgetPasswordSms(data: SendUserForgetPasswordSmsParams): Promise<boolean> {
  return merchantInstance.post(
    '/light/operation/app/staffRegister/send_forget_password_sms',
    data,
    {
      headers: {
        'Content-Type': ContentType.APPLICATION_X_WWW_FORM_URLENCODED,
      },
    },
  )
}

/**
 * @summary 重置用户密码
 * @description 验证短信验证码并重置用户密码
 * @param data UserResetPasswordParams
 * @returns Promise<boolean>
 */
export function resetUserPassword(data: UserResetPasswordParams): Promise<boolean> {
  return merchantInstance.post('/light/operation/app/staffRegister/reset_password', data, {
    headers: {
      'Content-Type': ContentType.APPLICATION_X_WWW_FORM_URLENCODED,
    },
  })
}

/**
 * @summary 验证短信并注册服务兵
 * @description 验证短信验证码并完成服务兵注册
 * @param data RegisterParams
 * @returns Promise<boolean>
 */
export function registerUser(data: RegisterParams): Promise<boolean> {
  return merchantInstance.post('/light/operation/app/user/register', data, {
    headers: {
      'Content-Type': ContentType.APPLICATION_X_WWW_FORM_URLENCODED,
    },
  })
}

/**
 * @summary 发送注册短信验证码
 * @description 验证手机号并发送验证码
 * @param data SendRegisterSmsParams
 * @returns Promise<boolean>
 */
export function sendUserRegisterSms(data: SendRegisterSmsParams): Promise<boolean> {
  return merchantInstance.post('/light/operation/app/user/send_register_sms', data, {
    headers: {
      'Content-Type': ContentType.APPLICATION_X_WWW_FORM_URLENCODED,
    },
  })
}

/**
 * @summary 获取运维商服务兵列表接口
 * @description 获取运维商服务兵列表接口
 * @returns Promise<Staff[]>
 */
export function getStaffList(): Promise<Staff[]> {
  return merchantInstance.get('/light/operation/workOrder/staffList')
}
