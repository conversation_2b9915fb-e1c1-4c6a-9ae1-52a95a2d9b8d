<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';
import EchartWrapper from './EchartWrapper.vue';
import type { EChartsOption } from 'echarts/types/dist/shared';
import type { LineSeriesOption } from 'echarts/types/dist/shared';

// 线图配置接口
interface LineItem {
  name: string;     // 线的名称
  data: number[];   // 数据数组
  color?: string;   // 线条颜色
  yAxisIndex?: number; // Y轴索引，默认为0
  showSymbol?: boolean; // 是否显示标记点
  areaStyle?: any;   // 区域填充样式
  smooth?: boolean;  // 是否平滑曲线
  lineStyle?: any;   // 线条样式
}

// 定义属性
const props = defineProps({
  // X轴数据
  xAxisData: {
    type: Array as () => string[],
    default: () => [],
  },
  // 线数据
  lines: {
    type: Array as () => LineItem[],
    default: () => [],
  },
  // Y轴配置
  yAxis: {
    type: [Object, Array],
    default: () => ({}),
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true,
  },
  // 是否显示tooltip
  showTooltip: {
    type: Boolean,
    default: true,
  },
  // 图表高度
  height: {
    type: String,
    default: '300px',
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%',
  },
  // 网格配置
  grid: {
    type: Object,
    default: () => ({}),
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false,
  },
  // 自定义配置
  customOptions: {
    type: Object as () => EChartsOption,
    default: () => ({}),
  },
  // 自定义CSS类名
  className: {
    type: String,
    default: '',
  },
});

// 定义事件
const emit = defineEmits(['init', 'update', 'click']);

// 将线数据转换为echarts的series配置
const series = computed(() => {
  return props.lines.map(line => {
    const defaultLineStyle = {
      width: 2,
      color: line.color
    };

    return {
      name: line.name,
      type: 'line' as const,  // 使用as const确保类型为'line'而不是string
      data: line.data,
      smooth: line.smooth !== undefined ? line.smooth : true,
      symbol: line.showSymbol ? 'circle' : 'none',
      symbolSize: 4,
      yAxisIndex: line.yAxisIndex || 0,
      lineStyle: line.lineStyle || defaultLineStyle,
      areaStyle: line.areaStyle,
      color: line.color
    } as LineSeriesOption;  // 显式类型断言为LineSeriesOption
  });
});

// 计算Y轴配置
const yAxisConfig = computed(() => {
  if (Array.isArray(props.yAxis)) {
    return props.yAxis;
  }

  return [props.yAxis];
});

// 计算图表配置
const chartOptions = computed<EChartsOption>(() => {
  const options: EChartsOption = {
    xAxis: {
      type: 'category',
      data: props.xAxisData,
    },
    yAxis: yAxisConfig.value,
    series: series.value,
    grid: {
      left: 30,
      right: 20,
      top: 30,
      bottom: 30,
      containLabel: true,
      ...props.grid
    }
  };

  // 添加图例配置
  if (props.showLegend) {
    options.legend = {
      type: 'plain',
      icon: 'rect',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 20,
      bottom: 0
    };
  }

  // 添加tooltip配置
  if (props.showTooltip) {
    options.tooltip = {
      trigger: 'axis',
      axisPointer: {
        type: 'line'
      }
    };
  }

  // 合并自定义配置
  return { ...options, ...props.customOptions };
});

// 图表初始化回调
function onChartInit(chart: any) {
  emit('init', chart);
}

// 图表更新回调
function onChartUpdate(chart: any) {
  emit('update', chart);
}

// 图表点击回调
function onChartClick(params: any) {
  emit('click', params);
}
</script>

<template>
  <EchartWrapper
    class="line-chart"
    :class="className"
    :options="chartOptions"
    :height="height"
    :width="width"
    :loading="loading"
    :auto-merge="false"
    @init="onChartInit"
    @update="onChartUpdate"
    @click="onChartClick"
  />
</template>

<style scoped lang="scss">
.line-chart {
  position: relative;
}
</style>
