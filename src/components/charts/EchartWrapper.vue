<script>
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'

export default {
  name: 'EchartWrapper',
  inheritAttrs: false,
  props: {
    options: {
      type: Object,
      required: true,
    },
    height: {
      type: String,
      default: '300px',
    },
    width: {
      type: String,
      default: '100%',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    theme: {
      type: String,
      default: '',
    },
  },
  emits: ['init', 'update', 'click'],
  setup(props, { emit }) {
    const chartRef = ref(null)
    const chartInstance = ref(null)
    const chartId = ref(`echart-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`)
    const isInitialized = ref(false)

    // 处理 renderjs 事件
    function onRenderEvent(event) {
      const { type, data } = event.detail
      if (type === 'click') {
        emit('click', data)
      } else if (type === 'init') {
        chartInstance.value = data
        isInitialized.value = true
        emit('init', data)
      } else if (type === 'update') {
        emit('update', data)
      }
    }

    // 监听配置变化
    watch(
      () => props.options,
      () => {
        if (isInitialized.value) {
          emit('update', chartInstance.value)
        }
      },
      { deep: true }
    )

    // 监听加载状态
    watch(
      () => props.loading,
      () => {
        // renderjs 会自动处理加载状态变化
      }
    )

    // 处理窗口大小变化
    function handleWindowResize() {
      // #ifdef H5 || APP-PLUS
      if (chartRef.value && chartRef.value.resize) {
        chartRef.value.resize()
      }
      // #endif
    }

    onMounted(() => {
      uni.onWindowResize(handleWindowResize)
    })

    onUnmounted(() => {
      // renderjs 会自动处理清理
    })

    // 小程序端触摸事件处理
    function onTouchStart(event) {
      console.log('Touch start:', event)
    }

    function onTouchMove(event) {
      console.log('Touch move:', event)
    }

    function onTouchEnd(event) {
      console.log('Touch end:', event)
    }

    return {
      chartRef,
      chartInstance,
      chartId,
      onRenderEvent,
      onTouchStart,
      onTouchMove,
      onTouchEnd,
      getChartInstance: () => chartInstance.value,
      getTheme: () => props.theme,
      resize: () => {
        if (chartRef.value && chartRef.value.resize) {
          chartRef.value.resize()
        }
      },
      showLoading: () => {
        if (chartRef.value && chartRef.value.showLoading) {
          chartRef.value.showLoading()
        }
      },
      hideLoading: () => {
        if (chartRef.value && chartRef.value.hideLoading) {
          chartRef.value.hideLoading()
        }
      },
    }
  }
}
</script>

<template>
  <view :class="['echart-wrapper', $attrs.class]" :style="{ height: height, width: width }">
    <!-- #ifdef H5 || APP-PLUS -->
    <view
      ref="chartRef"
      :id="chartId"
      :change:options="echartRender.updateOptions"
      :options="options"
      :change:loading="echartRender.updateLoading"
      :loading="loading"
      :change:theme="echartRender.updateTheme"
      :theme="theme"
      @renderEvent="onRenderEvent"
      :style="{ height: '100%', width: '100%' }"
    ></view>
    <!-- #endif -->

    <!-- #ifdef MP -->
    <!-- 小程序端使用canvas实现 -->
    <canvas
      ref="chartRef"
      :id="chartId"
      :canvas-id="chartId"
      type="2d"
      :style="{ height: '100%', width: '100%' }"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
    ></canvas>
    <!-- #endif -->
  </view>
</template>





<!-- renderjs 脚本 -->
<script module="echartRender" lang="renderjs">
let echarts = null
let chartInstances = new Map()

// 加载 ECharts 库
function loadEcharts() {
  return new Promise((resolve, reject) => {
    if (typeof window !== 'undefined' && window.echarts) {
      echarts = window.echarts
      resolve(echarts)
    } else {
      // 动态加载 echarts
      const script = document.createElement('script')
      script.src = './static/js/echarts.min.js'
      script.onload = () => {
        echarts = window.echarts
        resolve(echarts)
      }
      script.onerror = () => {
        reject(new Error('Failed to load echarts'))
      }
      document.head.appendChild(script)
    }
  })
}

// 初始化图表
function initChart(element, options, theme) {
  return new Promise((resolve, reject) => {
    if (!echarts) {
      loadEcharts().then(() => {
        createChart(element, options, theme, resolve, reject)
      }).catch(reject)
    } else {
      createChart(element, options, theme, resolve, reject)
    }
  })
}

let retryCount = 0
// 创建图表实例
function createChart(element, options, theme, resolve, reject) {
  try {
    const chartId = element.id
    // 确保元素有尺寸
    if (!element.offsetWidth || !element.offsetHeight) {
      if(retryCount > 10) {
        return;
      }
      retryCount++
      // 等待元素渲染完成
      setTimeout(() => {
        createChart(element, options, theme, resolve, reject)
      }, 100)

      return
    }

    // 销毁已存在的实例
    const existingInstance = chartInstances.get(chartId)
    if (existingInstance) {
      existingInstance.dispose()
    }

    // 创建新实例
    const chartInstance = echarts.init(element, theme)
    chartInstances.set(chartId, chartInstance)
    // 设置配置
    if (options) {
      chartInstance.setOption(options, true)
    }

    // 绑定事件
    chartInstance.on('click', (params) => {
      const event = new CustomEvent('renderEvent', {
        detail: { type: 'click', data: params }
      })
      element.dispatchEvent(event)
    })

    // 通知初始化完成
    const initEvent = new CustomEvent('renderEvent', {
      detail: { type: 'init', data: chartInstance }
    })
    element.dispatchEvent(initEvent)

    resolve(chartInstance)
  } catch (error) {
    console.error('Chart creation error:', error)
    reject(error)
  }
}

// 更新图表配置
function updateChart(chartId, options) {
  const chartInstance = chartInstances.get(chartId)
  if (chartInstance && options) {
    chartInstance.setOption(options, true)
    setTimeout(() => {
      chartInstance.resize()
    }, 0)
  }
}

// 显示加载动画
function showLoading(chartId, config) {
  const chartInstance = chartInstances.get(chartId)
  if (chartInstance) {
    chartInstance.showLoading(config || {
      text: '加载中...',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      textColor: '#909399',
    })
  }
}

// 隐藏加载动画
function hideLoading(chartId) {
  const chartInstance = chartInstances.get(chartId)
  if (chartInstance) {
    chartInstance.hideLoading()
  }
}

// 调整图表大小
function resizeChart(chartId) {
  const chartInstance = chartInstances.get(chartId)
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 销毁图表
function disposeChart(chartId) {
  const chartInstance = chartInstances.get(chartId)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstances.delete(chartId)
  }
}

export default {
  mounted() {
  },

  methods: {
    // 监听选项变化
    updateOptions(newVal, oldVal, ownerInstance, instance) {
      if (newVal && instance) {
        const element = instance.$el
        const chartId = element.id

        // 如果是第一次设置选项，初始化图表
        if (!chartInstances.get(chartId)) {
          // 从 ownerInstance 获取主题信息
          const theme = ownerInstance.callMethod ? ownerInstance.callMethod('getTheme') : ''
          initChart(element, newVal, theme).then((chart) => {
            // console.log('Chart initialized via renderjs:', chartId)
          }).catch((error) => {
            console.error('Chart initialization failed:', error)
          })
        } else {
          // 更新现有图表
          updateChart(chartId, newVal)
        }
      }
    },

    // 监听加载状态变化
    updateLoading(newVal, oldVal, ownerInstance, instance) {
      if (instance) {
        const element = instance.$el
        const chartId = element.id
        if (newVal) {
          showLoading(chartId)
        } else {
          hideLoading(chartId)
        }
      }
    },

    // 监听主题变化
    updateTheme(newVal, oldVal, ownerInstance, instance) {
      if (newVal !== oldVal && instance) {
        const element = instance.$el
        const chartId = element.id

        // 重新初始化图表以应用新主题
        const existingInstance = chartInstances.get(chartId)
        if (existingInstance) {
          // 获取当前配置
          const currentOptions = existingInstance.getOption()
          disposeChart(chartId)
          initChart(element, currentOptions, newVal)
        }
      }
    },

    // 调整大小
    resize(chartId) {
      resizeChart(chartId)
    },

    // 显示加载
    showLoading(chartId) {
      showLoading(chartId)
    },

    // 隐藏加载
    hideLoading(chartId) {
      hideLoading(chartId)
    },

    // 销毁图表
    dispose(chartId) {
      disposeChart(chartId)
    }
  }
}
</script>

<style scoped lang="scss">
.echart-wrapper {
  position: relative;
}
</style>
