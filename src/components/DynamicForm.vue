<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import FileUpload from './FileUpload.vue'
import { getValueByPath, setValueByPath } from '@/utils/path'

/**
 * 表单项配置类型定义
 */
export interface FormItemConfig {
  type: 'input' | 'select' | 'textarea' | 'upload' | 'custom' // 表单项类型
  field: string // 表单字段名，支持嵌套路径如 'user.address' 或 'items.0.name'
  label?: string // 表单项标签
  placeholder?: string // 占位符
  required?: boolean // 是否必填
  options?: any[] // 选择器选项
  apiKey?: string // 远程获取选项的API键
  customSlot?: string // 自定义插槽名称，替代customComponent
  customProps?: Record<string, any> // 自定义组件属性
  disabled?: boolean // 是否禁用
  readonly?: boolean // 是否只读
  rules?: any[] // 校验规则
  col?: number // 栅格宽度
  attrs?: Record<string, any> // 其他属性
}

const props = defineProps<{
  config: FormItemConfig[] // 表单配置
  labelPosition?: 'top' | 'left' | 'none' // 标签位置
  hideLabels?: boolean // 是否隐藏标签
  apiData?: Record<string, any[]> // 远程选项数据
}>()

// 使用defineModel替代手动的双向绑定逻辑
const modelValue = defineModel<Record<string, any>>({ required: true, default: () => ({}) })
const toast = useToast()

const emit = defineEmits<{
  (e: 'fetch-options', key: string): void // 请求下拉选项数据
}>()

const formRef = ref()

// 配置对象，根据配置显示不同的表单项
const formConfig = computed(() => {
  return props.config.map(item => {
    // 设置默认必填规则
    const rules = item.rules || []
    if (item.required && !rules.some(rule => rule.required)) {
      rules.unshift({ required: true, message: `请${item.type === 'select' ? '选择' : '输入'}${item.label || item.field}` })
    }

    return {
      ...item,
      rules,
      required: rules.some(ele => ele.required)
    }
  })
})

const rules = computed(() => {
  return props.config.reduce((result: Record<string, any>, curr) => {
    // 设置默认必填规则
    const rules = curr.rules || []
    if (curr.required && !rules.some(rule => rule.required)) {
      rules.unshift({ required: true, message: `请${curr.type === 'select' ? '选择' : '输入'}${curr.label || curr.field}` })
    }
    // 不修改原始字段名，直接将rules添加到结果中
    result[curr.field] = rules;
    return result
  }, {})
})

// 获取选项，优先使用传入的 options，然后是 apiData
const getOptions = (item: FormItemConfig) => {
  if (item.options) {
    return item.options
  }

  if (item.apiKey && props.apiData && props.apiData[item.apiKey]) {
    return props.apiData[item.apiKey]
  }

  return []
}

const getOptionLabel = (item: FormItemConfig, value: string | number) => {
  if (item.options) {
    return item.options.find(item => item.value === value)?.label || ''
  }

  if (item.apiKey && props.apiData && props.apiData[item.apiKey]) {
    return props.apiData[item.apiKey].find(item => item.value === value)?.label || ''
  }

  return ''
}

// 验证表单
const validate = async () => {
  return await formRef.value.validate()
}

// 提交表单
const validateWithHandleError = async () => {
  const { valid, errors } = await validate()
  if (valid) {
    return true
  } else {
    if (errors && errors.length > 0) {
      toast.warning(errors[0].message || '请检查表单填写')
    }
    return false
  }
}

// 重置表单
const reset = () => {
  if (formRef.value) {
    formRef.value.reset()
  }
}

onMounted(() => {
  // 请求下拉选项数据
  props.config.forEach(item => {
    if (item.type === 'select' && item.apiKey && (!props.apiData || !props.apiData[item.apiKey])) {
      emit('fetch-options', item.apiKey)
    }
  })
})

// 暴露方法给父组件
defineExpose({
  validate,
  validateWithHandleError,
  reset,
  formRef
})
</script>

<template>
  <wd-form ref="formRef" :model="modelValue" class="dynamic-form" :rules="rules">
    <template v-for="(item) in formConfig" :key="item.field">
      <wd-form-item :prop="item.field" :class="['form-item', item.required ? 'is-required' : '']" :label="item.label">
        <!-- 输入框 -->
        <template v-if="item.type === 'input'">
          <view class="input-wrapper">
            <wd-input :model-value="getValueByPath<string>(modelValue, item.field) || ''"
              @update:model-value="val => setValueByPath(modelValue, item.field, val)"
              :placeholder="item.placeholder || `请输入${item.label}`" :readonly="item.disabled" custom-class="form-input"
              v-bind="item.attrs || {}" />
            <!-- 右侧插槽支持 -->
            <slot v-if="item.attrs?.rightSlot" :name="`right-${item.field}`"></slot>
          </view>
        </template>

        <!-- 选择器 -->
        <template v-else-if="item.type === 'select'">
          <wd-select-picker
            :model-value="getValueByPath<string | number | boolean | (string | number | boolean)[]>(modelValue, item.field) || ''"
            @update:model-value="val => setValueByPath(modelValue, item.field, val)" :columns="getOptions(item)"
            :disabled="item.disabled" use-default-slot :type="item.attrs?.type || 'radio'" custom-class="form-picker"
            v-bind="item.attrs || {}">
            <text :class="['picker__text', !getValueByPath(modelValue, item.field) && 'picker__text--placeholder']">{{
              getOptionLabel(item, getValueByPath<string | number>(modelValue, item.field) || '') || `请选择${item.label}`
            }}</text>
            <text class="i-carbon-chevron-down picker__icon"></text>
          </wd-select-picker>
        </template>

        <!-- 文本域 -->
        <template v-else-if="item.type === 'textarea'">
          <wd-textarea :model-value="getValueByPath<string>(modelValue, item.field) || ''"
            @update:model-value="val => setValueByPath(modelValue, item.field, val)"
            :placeholder="item.placeholder || `请输入${item.label}`" :maxlength="item.attrs?.maxlength || 500"
            :show-word-limit="item.attrs?.showWordLimit ?? true" :readonly="item.disabled" custom-class="form-textarea"
            :custom-style="item.attrs?.customStyle || ''" v-bind="item.attrs || {}" />
        </template>

        <!-- 上传控件 -->
        <template v-else-if="item.type === 'upload'">
            <view>
              <FileUpload :file-list="getValueByPath<any>(modelValue, item.field) || []"
                @update:file-list="(val: any) => setValueByPath(modelValue, item.field, val)"
                :limit="item.attrs?.maxCount || 5"
                :source-type="item.attrs?.sourceType || ['camera']"
                v-bind="item.customProps || {}" />
              <view class="mt-2 text-gray-400 text-left" v-if="item.attrs?.hint">{{ item.attrs.hint }}</view>
          </view>
        </template>

        <!-- 自定义插槽 -->
        <template v-else-if="item.type === 'custom' && item.customSlot">
          <slot :name="item.customSlot" :model="modelValue" :field="item.field" :extra="item.attrs?.extraData"
            :value="getValueByPath(modelValue, item.field)" :props="item.customProps || {}"></slot>
        </template>
      </wd-form-item>
    </template>

    <slot></slot>
  </wd-form>
</template>

<style scoped lang="scss">
.dynamic-form {
  width: 100%;

  :deep(.wd-form-item) {
    padding-left: 0px;

    &:first-child {
      .wd-cell__wrapper {
        padding-top: 0px;
      }
    }

    &:last-child {
      .wd-cell__wrapper {
        padding-bottom: 0px;
      }
    }
  }

  :deep(.wd-cell__left) {
    min-width: 100%!important;
    max-width: 100%!important;
  }

  .is-required {
    :deep(.wd-cell__left) {

      .wd-cell__title {
        margin-left: 12px;
      }

      &::after {
        position: absolute;
        left: 0;
        top: 2px;
        content: "*";
        font-size: var(--wot-cell-required-size, 18px);
        line-height: 1.1;
        color: #fa4350;
      }
    }
  }

  :deep(.wd-cell__wrapper) {
    flex-direction: column;
    padding-right: 0px;

    &:first-child {
      padding-top: 0px;
    }

    .wd-cell__right {
      width: 100%;
      margin-top: 8px;

      .wd-cell__value {
        display: flex;
        flex-direction: column;
        min-width: 0px;

        .input-wrapper {
          display: flex;
          width: 100%;
        }

        .wd-input {
          flex: 1;
          height: 36px;
          border: 1px solid #DEE2E6;
          border-radius: 4px;
          box-sizing: border-box;
          padding: 0px 15px;

          &::after {
            content: none;
          }
        }

        .wd-select-picker {
          height: 36px;
          border: 1px solid #DEE2E6;
          border-radius: 4px;
          box-sizing: border-box;
          padding: 0px 15px;

          .wd-select-picker__field {
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .picker__text {
            color: #303133;

            &--placeholder {
              color: #C0C4CC;
            }
          }
        }

        .wd-textarea {
          border: 1px solid #DEE2E6;
          border-radius: 4px;
          width: auto !important;
          padding-left: 4px;
          &::after {
            content: none;
          }
        }
      }
    }
  }
}
</style>
