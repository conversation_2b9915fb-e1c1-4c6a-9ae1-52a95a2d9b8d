<template>
  <view class="search-section" :class=" mode === 'light' ? 'bg-white p-3 pb-2 flex items-center' : 'p-3 pb-2 flex items-center'">
    <view
      :class="[
        'search-input-wrapper flex-1 rounded-md pl-2 pr-1 py-1 flex items-center shadow-sm',
        mode === 'light' ? 'bg-[#F7F7F5]' : 'bg-white border border-[#eee]',
      ]"
    >
      <text class="i-carbon-search text-[#9A9A9A] mr-2"></text>
      <input
        :value="keyword"
        @input="onInput"
        class="flex-1 text-sm bg-transparent focus:outline-none mr-2"
        :placeholder="placeholder"
        placeholder-class="text-[#9A9A9A] text-xs"
        confirm-type="search"
        @confirm="onSearch"
      />
      <text
        v-if="keyword"
        class="i-carbon-close-filled text-[#9A9A9A] mr-2"
        @click="clearKeyword"
      ></text>
      <button
        class="search-button bg-primary text-white text-sm font-medium px-4 py-1.5 rounded-md active:opacity-80 shadow-sm flex-shrink-0"
        @click="onSearch"
      >
        搜索
      </button>
    </view>
    <view v-if="showFilter" class="ml-2">
      <slot
        name="filter-trigger"
        :icon-classes="filterIconComputedClasses"
        :text-classes="filterTextComputedClasses"
        :mode="mode"
      >
        <view
          :class="filterIconComputedClasses"
          @click="onFilterClick"
        >
          <text :class="filterTextComputedClasses"></text>
        </view>
      </slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'

const modelValue = defineModel<string>('modelValue', { default: '' })

const props = defineProps({
  placeholder: {
    type: String,
    default: '请输入关键字',
  },
  showFilter: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'default',
    validator: (value: string) => ['default', 'light'].includes(value),
  },
})

const emit = defineEmits(['search', 'filter'])

const filterIconComputedClasses = computed(() => [
  'f-c-c w-10 h-10 rounded-lg shadow-sm',
  props.mode === 'light' ? 'bg-[#F7F7F5]' : 'bg-white',
]);

const filterTextComputedClasses = computed(() => [
  'i-carbon-filter',
  'text-lg',
  props.mode === 'light' ? 'text-[#4B4B4B]' : 'text-[#666]',
]);

const keyword = ref(modelValue.value)

watch(
  () => modelValue.value,
  newVal => {
    keyword.value = newVal
  },
)

const onInput = (e: any) => {
  const value = e.detail.value
  keyword.value = value
  modelValue.value = value
}

const clearKeyword = () => {
  keyword.value = ''
  modelValue.value = ''
  emit('search', '')
}

const onSearch = () => {
  emit('search', keyword.value)
}

const onFilterClick = () => {
  emit('filter')
}
</script>

<style scoped>
.search-section {
  width: 100%;
  box-sizing: border-box;
}
</style>
