### 监控列表页面模板提示语/规则

### 1. 模板用途与核心特性

- **用途**: 展示电站监控列表，支持搜索和分页加载。
- **核心特性**:
  - 顶部搜索区域，支持按电站编号、户主姓名或手机号码搜索。
  - 使用 `z-paging` 组件实现列表的下拉刷新和上拉加载更多。
  - 列表项以卡片形式展示电站基本信息。
  - 点击列表项（或特定按钮）可跳转到地图导航（如果经纬度存在）。
  - 虚拟列表优化长列表性能。

### 2. Vue 模板结构概要 (`<template>`)

- **整体布局**: `.monitor-page` (flex column, 100vh)
  - **搜索区域**: `.search-section`
    - 输入框 (`input`): `v-model="keyword"`, `@confirm="onSearch"`
      - 搜索图标
      - `placeholder`
    - 搜索按钮 (`button`): `@click="onSearch"`
  - **电站列表区域**: `z-paging` 组件
    - `ref="paging"`
    - `v-model="stationList"`
    - `@query="queryList"`
    - `:default-page-size="pageSize"`
    - `use-virtual-list`
    - 空状态插槽 (`template #empty`)
    - **列表项卡片**: `.station-card` (v-for="item in stationList")
      - 卡片头部 (`.card-header`):
        - 电站编号 (`.title`): `{{ item.stationCode }}`
        - (可选)导航按钮 (`.navigate-button`): `@click.stop="handleNavigate(item)"`
      - 卡片元信息 (`.card-meta`): 创建时间 `{{ item.createdAt }}`
      - 卡片主体 (`.card-body`):
        - 电站名称 (`.detail-item`): `{{ item.name }}`
        - 电站地址 (`.detail-item`): `{{ item.address }}`
        - 联系方式 (`.detail-item`): `{{ item.phone }}`

### 3. 脚本逻辑概要 (`<script setup lang="ts">`)

- **响应式数据**:
  - `keyword = ref('')`: 搜索关键词。
  - `paging = ref<ZPagingRef>()`: `z-paging` 组件实例引用。
  - `stationList = ref<Station[]>()`: 电站列表数据。
  - `pageSize = ref(10)`: 分页大小。
- **主要方法**:
  - `onSearch()`: 触发 `z-paging` 重新加载数据。
  - `queryList(pageNum: number, pSize: number)`:
    - 构建请求参数 `params: StationPageParams`，包含 `pageNum`, `pageSize`。
    - 根据 `keyword.value` 设置 `params.phone` 或 `params.name`/`params.stationCode`。
    - 调用 `getStationPage(params)` API。
    - 成功时，调用 `paging.value?.completeByTotal(res.content, res.totalElements)`。
    - 失败时，调用 `paging.value?.complete(false)`。
  - `handleNavigate(item: Station)`:
    - 检查 `item.longitude` 和 `item.latitude` 是否存在。
    - 若存在，调用 `uni.openLocation` 打开地图。
    - 若不存在，调用 `uni.showToast` 提示位置信息不完整。
- **类型导入**:
  - `import type { Station, StationPageParams } from '@/types/api/Station'`
  - `import { getStationPage } from '@/api/station'`

### 4. 页面路由配置 (`<route lang="json">`)

- **`navigationStyle: "default"`**
- **`navigationBarTitleText: "监控中心"`**

### 5. 样式概要 (`<style scoped lang="scss">`)

- **页面级样式 (`.monitor-page`)**: flex 布局, 高度占满视口, 背景色。
- **搜索区样式 (`.search-section`, `.search-input-wrapper`, `.search-button`)**: 背景色, 内外边距, 圆角, 阴影, flex 布局。
- **列表分页组件样式 (`.station-list-paging`)**: flex: 1 占满剩余空间。
- **列表内容区 (`.station-list-content`)**: 内边距。
- **电站卡片样式 (`.station-card`)**: 背景色, 圆角, 内外边距, 阴影。
  - 卡片头部 (`.card-header`): flex 布局 (space-between), 标题字体样式。
  - (可选)导航按钮 (`.navigate-button`): 背景色, 颜色, 字体大小, 内边距, flex 布局。
  - 卡片元信息 (`.card-meta`): 字体大小, 颜色, 边框, 内外边距。
  - 卡片主体 (`.card-body`, `.detail-item`, `.label`, `.value`): flex 布局, 字体大小, 颜色, 间距, 换行处理。
- **placeholder 样式 (`:deep(.uni-input-placeholder)`)**: 颜色, 字体大小。

### 6. 可配置项/扩展点

- **搜索逻辑**: 可扩展更复杂的搜索条件或字段。
- **列表项展示内容**: 可根据需求增删卡片中显示的电站信息字段。
- **列表项交互**: 可增加更多操作按钮或点击事件。
- **筛选功能**: (当前代码中未完全实现，但有占位样式 `.filter-icon`, `.filter-popup-content`) 可考虑加入独立的筛选器组件或弹出层。
- **API 接口**: `getStationPage` 的具体实现和返回数据结构。
- **类型定义**: `Station`, `StationPageParams` 的具体字段。
