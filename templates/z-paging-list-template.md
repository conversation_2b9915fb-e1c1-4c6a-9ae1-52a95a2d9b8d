### ZPaging 组件列表页面模板

### 1. 模板用途与核心特性

- **用途**: 该模板提供了一个使用 `z-paging` 组件实现通用分页列表页面的基础结构。适用于需要展示数据列表、支持下拉刷新、上拉加载更多的场景。
- **核心特性**:
  - 使用 `z-paging` 组件作为列表的核心分页和加载控制器。
  - 支持通过 `@query` 事件异步加载列表数据。
  - 列表数据通过 `v-model` 进行双向绑定。
  - 提供了列表项渲染的基本结构。
  - 包含数据请求成功和失败时的 `z-paging` 状态更新逻辑。
  - 支持通过 `paging.value?.reload()` 方法刷新列表。
  - 可配置的 `default-page-size`。

### 2. Vue 模板结构概要 (`<template>`)

```html
<!-- 页面根元素，通常需要设置高度或flex:1来确保z-paging正常工作 -->
<view class="page-container">
  <!-- (可选) 搜索栏或筛选区域 -->
  <!-- <SearchBar v-model="keyword" @search="reloadList" /> -->

  <!-- (可选) Tabs 切换 -->
  <!--
  <wd-tabs v-model="activeTab" @change="onTabChange">
    <wd-tab v-for="tab in tabs" :key="tab.value" :title="tab.title" :name="tab.value" />
  </wd-tabs>
  -->

  <z-paging
    ref="paging"
    v-model="dataList"
    class="list-paging-component" <!-- 自定义z-paging的样式类 -->
    :fixed="false" <!--  对于页面整体滚动，通常设置为 false；若希望列表区域固定高度内部滚动，可设为true并确保父容器有固定高度 -->
    @query="fetchDataList"
    :default-page-size="queryParams.pageSize" <!-- 与 queryParams 中的 pageSize 保持一致 -->
    :auto-hide-loading-after-first-loaded="false"
    :show-loading-more-when-reload="true"
  >
    <!-- 列表内容区域 -->
    <view class="list-content-wrapper p-3 space-y-3"> <!-- UnoCSS classes for padding and spacing -->
      <view
        v-for="item in dataList"
        :key="item.id" <!-- 确保 item 有一个唯一的 id 属性 -->
        class="list-item-card bg-white rounded-lg shadow p-3" <!-- 示例卡片样式 -->
        @click="handleItemClick(item.id)"
      >
        <!-- 列表项内部结构，根据实际业务需求定制 -->
        <view class="flex">
          <image
            :src="item.imageUrl || '/static/logo.png'" <!-- 示例图片 -->
            mode="aspectFill"
            class="w-20 h-20 rounded-md bg-gray-200 mr-3"
          />
          <view class="flex-1">
            <text class="item-title block text-base font-medium text-gray-800 truncate">{{ item.name }}</text>
            <text class="item-description block text-sm text-gray-500 mt-1 truncate">{{ item.description }}</text>
          </view>
        </view>
        <!-- 结束：列表项内部结构 -->
      </view>
    </view>

    <!-- (可选) 自定义空状态视图 -->
    <!--
    <template #empty>
      <view class="custom-empty-view flex flex-col items-center justify-center p-10">
        <image src="/static/empty-custom.png" class="w-40 h-40" />
        <text class="text-gray-500 mt-4">这里什么都没有呢</text>
      </view>
    </template>
    -->
  </z-paging>
</view>
```

### 3. 脚本逻辑概要 (`<script setup lang="ts">`)

```typescript
import { ref, reactive, onMounted } from 'vue'
// ZPagingInstance已经通过tsconfig/types.json 中的 types 引入，确保在项目中正确配置了类型路径。
// 或者简单使用 `any` 类型: const paging = ref<any>(null)
import type { ZPagingInstance } from 'z-paging/components/z-paging/js/z-paging-utils' // 假设这是正确的路径
import type { PaginatedContent } from '@/service/types' // 项目中定义的标准分页响应结构
// import { getEntityPage } from '@/api/entity' // 替换为实际的获取分页数据的 API 函数
// import type { Entity, EntityPageParams } from '@/types/api/Entity' // 替换为实际的列表项类型和分页请求参数类型

// 模拟类型，请替换为实际类型
interface ListItemType {
  id: number | string
  name: string
  description?: string
  imageUrl?: string
  // ... 其他属性
}
interface ListPageParams {
  pageNum: number
  pageSize: number
  keyword?: string
  type?: string // 示例：用于 tab 切换或筛选
  // ... 其他查询参数
}

const paging = ref<ZPagingInstance | null>(null)
const dataList = ref<ListItemType[]>([])

// 列表查询参数
const queryParams = reactive<ListPageParams>({
  pageNum: 1,       // 页码，z-paging 会自动管理，但可用于初始请求
  pageSize: 10,     // 每页数量，应与 z-paging 的 :default-page-size 同步
  keyword: '',      // 示例：搜索关键词
  type: 'all',      // 示例：类型筛选
})

// tabs 示例
// const tabs = ref([
//   { title: '全部', value: 'all' },
//   { title: '类型一', value: 'type1' },
// ])
// const activeTab = ref('all')

// onMounted(() => {
//   // 可在此处进行一些初始化操作，例如获取筛选条件等
//   // paging.value?.reload() // 如果需要在挂载后立即加载数据，可以调用
// })

/**
 * @description: 核心数据获取函数，由 z-paging 的 @query 事件触发
 * @param {number} pageNo 当前页码
 * @param {number} pSize 每页数量
 */
const fetchDataList = async (pageNo: number, pSize: number) => {
  try {
    const params: ListPageParams = {
      ...queryParams, // 合并基础查询参数
      pageNum: pageNo,
      pageSize: pSize,
      // type: activeTab.value, // 如果有 tab 切换，则加入当前 tab 值
    }

    // 实际项目中替换为真实的 API 调用
    // const response: PaginatedContent<ListItemType> = await getEntityPage(params)

    // -------- Mock API Call Start --------
    const mockApiCall = (p: ListPageParams): Promise<PaginatedContent<ListItemType>> => {
      return new Promise(resolve => {
        setTimeout(() => {
          const items: ListItemType[] = []
          const totalElements = 55 // 假设总数据量
          const startId = (p.pageNum - 1) * p.pageSize
          for (let i = 0; i < p.pageSize; i++) {
            const currentId = startId + i
            if (currentId >= totalElements) break
            items.push({
              id: currentId + 1,
              name: `数据项 ${currentId + 1} (页: ${p.pageNum}, 类型: ${p.type || 'N/A'})`,
              description: `这是第 ${currentId + 1} 条数据的描述信息。`,
            })
          }
          resolve({
            content: items,
            totalElements,
            totalPages: Math.ceil(totalElements / p.pageSize),
            // ... biz_rules.md 中定义的其他分页响应字段
            success: true,
            error: null,
            message: null,
            errorMap: {}
          })
        }, 800)
      })
    }
    const response = await mockApiCall(params)
    // -------- Mock API Call End --------

    if (response.success && response.content) {
      paging.value?.completeByTotal(response.content, response.totalElements)
    } else {
      // 处理 API 返回的业务错误
      console.error('API error:', response.message || response.error)
      paging.value?.complete(false)
    }
  } catch (error) {
    console.error('Failed to fetch data list:', error)
    paging.value?.complete(false) // 网络错误或其他异常，调用 complete(false)
  }
}

// 重新加载列表数据 (例如：搜索、筛选、Tab切换后)
const reloadList = () => {
  // queryParams.pageNum = 1 // z-paging reload 会自动将页码重置为第一页
  paging.value?.reload()
}

// 示例：Tab 切换处理
// const onTabChange = (event: any) => {
//   const newTabValue = event.name || event.detail.value // 取决于UI库的事件结构
//   if (activeTab.value !== newTabValue) {
//     activeTab.value = newTabValue
//     queryParams.type = newTabValue // 更新查询参数
//     reloadList()
//   }
// }

// 示例：列表项点击处理
const handleItemClick = (id?: number | string) => {
  if (!id) return
  uni.navigateTo({
    url: `/pages/detail-page/index?id=${id}`, // 替换为实际的详情页路径
  })
}
```

### 4. 页面路由配置 (`<route lang="json">`)

```json
{
  "style": {
    "navigationStyle": "default",  // "default" 或 "custom"
    "navigationBarTitleText": "列表页面" // 页面标题
  }
  // 如果使用了 wot-design-uni 或其他需要全局注册的组件，
  // 并且没有使用 vite-plugin-uni-components 自动导入，
  // 可能需要在这里配置 usingComponents，但通常不推荐，优先自动导入。
  // "usingComponents": {
  //   "wd-tab": "/uni_modules/wot-design-uni/components/wd-tab/wd-tab",
  //   "wd-tabs": "/uni_modules/wot-design-uni/components/wd-tabs/wd-tabs"
  // }
}
```
*根据项目配置 (`.clinerules/project.md` 提及 `vite-plugin-uni-components`)，`usingComponents` 通常是不需要的。*

### 5. 样式概要 (`<style scoped lang="scss">`)

```scss
// 页面容器，确保 z-paging 可以撑满
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh; // 或者通过其他方式确保父容器高度
  background-color: #f4f4f5; // 示例背景色
}

// z-paging 组件本身的样式类
.list-paging-component {
  flex: 1; // 让 z-paging 占据剩余空间
  // 如果 :fixed="true"，需要给 z-paging 设置具体的高度
  // height: calc(100vh - var(--search-bar-height, 0px) - var(--tabs-height, 0px));
}

// 列表内容区域的包裹元素
.list-content-wrapper {
  // UnoCSS classes p-3 space-y-3 are applied in template
  // background-color: transparent; // 通常列表背景由 z-paging 或页面容器控制
}

// 单个列表项卡片
.list-item-card {
  // UnoCSS classes bg-white rounded-lg shadow p-3 are applied in template
  // 可在此处添加更复杂的卡片样式
  .item-title {
    // UnoCSS classes applied
  }
  .item-description {
    // UnoCSS classes applied
    // 多行省略示例
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  image {
    // UnoCSS classes applied
  }
}

// (可选) 自定义空状态视图样式
.custom-empty-view {
  // UnoCSS classes applied if used in template
  // display: flex;
  // flex-direction: column;
  // align-items: center;
  // justify-content: center;
  // padding: 40px;
  // image {
  //   width: 160px;
  //   height: 160px;
  // }
  // text {
  //   color: #909399;
  //   margin-top: 16px;
  // }
}
```

### 6. 可配置项/扩展点

- **API 集成**:
  - 替换 `getEntityPage` 为实际获取数据的 API 函数。
  - 调整 `ListItemType` 和 `ListPageParams` 以匹配实际的接口数据结构和请求参数。
  - 确保 API 响应遵循项目 `.clinerules/biz_rules.md` 中定义的分页格式。
- **列表项渲染**:
  - 完全自定义 `.list-item-card` 内部的 HTML 结构和样式以展示业务数据。
  - 可以将列表项抽取为独立的组件。
- **搜索与筛选**:
  - 实现顶部搜索栏、筛选按钮和弹出层等交互。
  - 更新 `queryParams` 和 `fetchDataList` 中的 `params` 来包含搜索和筛选条件。
  - 调用 `reloadList()` 方法以应用新的搜索/筛选条件。
- **Tabs 切换**:
  - 集成 `wd-tabs` 或其他 Tabs 组件。
  - 在 `onTabChange` 事件中更新 `queryParams` (例如 `queryParams.type`) 并调用 `reloadList()`。
- **z-paging 配置**:
  - 根据具体需求调整 `z-paging` 的各种属性，如 `refresher-enabled`, `empty-view-text`, `loading-more-no-more-text` 等。
  - 使用 `z-paging` 的插槽 (`#empty`, `#loading`, `#refresher` 等) 来自定义不同状态下的视图。
- **错误处理**:
  - 细化 `fetchDataList` 中的错误处理逻辑，例如根据错误类型给出不同的用户提示。
- **类型安全**:
  - 确保所有从 API 获取的数据和传递给组件的 props 都有准确的 TypeScript 类型定义。参考 `.clinerules/typescript_typing_guidelines.md`。
