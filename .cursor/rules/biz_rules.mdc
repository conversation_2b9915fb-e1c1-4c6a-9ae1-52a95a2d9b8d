---
description: 
globs: 
alwaysApply: false
---
# 编写代码规则

## 接口

- 请求返回的格式

```
{
  "result": [],
  "error": null,
  "message": null,
  "errorMap": {},
  "success": true
}
```

- 分页请求返回的格式

```
{
  "result": {
    content: [],
    totalPages: 0
  },
  "error": null,
  "message": null,
  "errorMap": {},
  "success": true
}
```

- 接口返回的 result 是数据，error 是错误时的信息，message 是成功时的消息，errorMap 是错误信息的映射，success 是是否成功

## 注释

**原则：** 代码本身应尽可能清晰自明。注释应用于解释复杂、不直观的逻辑，而不是描述显而易见的操作或开发过程。

**避免以下类型的注释：**

1.  **描述代码更改的注释：**

    - 例如：`// Added: ...`, `// Removed: ...`, `// Updated: ...`
    - 理由：版本控制系统（如 Git）已跟踪这些更改。

2.  **解释显而易见代码功能的注释：**

    - 例如：`// Clear the list` (当代码是 `list.clear()`)，`// 循环遍历用户` (当代码是 `for user in users:`)
    - 理由：代码本身已经清晰地表达了其功能。

3.  **解释代码意图、假设或数据结构的注释（当代码应通过命名和结构自解释时）：**
    - **假设性注释：**
      - 例如：`// Assuming photos is a single URL string, or comma-separated`
      - 例如：`// Assuming the work order ID is passed as 'id'`
      - 例如：`// 假设总共有5页数据`
    - **数据操作描述（应通过代码逻辑体现）：**
      - 例如：`// Populate faultSolutions and checkItems from the fetched data`
      - 例如：`// Or map from another field if remarks are stored`
    - **状态或存在性检查（应通过代码逻辑体现）：**
      - 例如：`// Ensure ID exists`
      - 例如：`// Default UI state`
    - 理由：如果代码逻辑复杂到需要这类注释，应首先考虑重构代码以提高其可读性。清晰的变量名、函数名和代码结构是首选。

**允许的注释：**

- **类型相关的注释：**
  - 例如：JSDoc 中的 `@type` 或 TypeScript 中的类型注解（尽管 TypeScript 的类型系统通常使其多余）。
  - 在某些动态类型语言或复杂类型推断场景下可能有帮助。
- **解释复杂算法或业务逻辑的注释：**
  - 当某段代码的逻辑确实非常复杂且难以通过代码本身完全表达时，可以添加注释。但仍应首先尝试简化代码。
- **TODO 或 FIXME 注释：**
  - 用于标记待办事项或已知问题，例如：`// TODO: Refactor this to improve performance`，`// FIXME: Potential race condition here`。

**特定文件类型的规则：**

- **Vue 文件中 `<style>` 标签内的 CSS 注释：**
  - 通常应移除，除非该注释对于理解复杂的 CSS 选择器或特定 hack 至关重要。

**总结：** 在添加注释前，请思考该注释是否真的必要，或者是否可以通过改进代码本身来消除注释的需求。
