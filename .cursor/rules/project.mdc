---
description:
globs:
alwaysApply: false
---
# UniApp Vite 模板项目架构指南

## 项目概述

这是一个基于 UniApp + Vue 3 + TypeScript + Vite 的跨平台应用开发模板，支持多端开发部署，这个项目主要是 App 平台。
# UniApp Vite 模板项目架构指南

## 项目概述

这是一个基于 UniApp + Vue 3 + TypeScript + Vite 的跨平台应用开发模板，支持多端开发部署，这个项目主要是 App 平台。

## 技术栈

- **框架**: Vue 3.5.13 + UniApp
- **构建工具**: Vite 5.2.x
- **语言**: TypeScript 5.7.3
- **包管理**: pnpm
- **状态管理**: Pinia 2.0.36
- **UI 组件库**:
  - uni-ui 1.5.7
  - wot-design-uni 1.7.1
  - wot-design-uni 文档地址: https://wot-design-uni.netlify.app/
  - wot-design-uni 组件使用说明: https://wot-design-uni.netlify.app/component/button.html
  - wot-design-uni github 地址: https://github.com/Moonofweisheng/wot-design-uni
- **数据请求**: @tanstack/vue-query 4.37.1 + @uni-helper/uni-network 0.20.0
- **工具库**:
  - @vueuse/core 12.7.0
  - dayjs 1.11.13
  - qs 6.5.3
- **CSS 预处理**: Sass
- **原子化 CSS**: UnoCSS

## 项目结构

```
├── .clinerules/          # 项目规则文档
├── build/                # 构建相关配置
│   ├── config/           # 构建配置
│   └── plugins/          # Vite 插件配置
├── src/                  # 源代码
│   ├── api/              # API 接口定义
│   ├── composables/      # 组合式函数
│   ├── constants/        # 常量定义
│   ├── layouts/          # 布局组件
│   ├── pages/            # 页面组件
│   ├── plugins/          # 插件配置
│   ├── service/          # 服务层
│   ├── static/           # 静态资源
│   ├── store/            # Pinia 状态管理
│   ├── styles/           # 全局样式
│   ├── uni_modules/      # UniApp 模块
│   └── utils/            # 工具函数
├── typings/              # 类型定义
└── ...配置文件           # 各类配置文件
```

## 多环境配置

项目支持多环境配置，包括：

- 开发环境 (development)
- 生产环境 (production)
- 预发布环境 (staging)

环境变量文件：

- `.env`：所有环境通用配置
- `.env.development`：开发环境配置
- `.env.production`：生产环境配置

## 多平台支持

项目支持以下平台的开发和构建：

- App (iOS/Android)

## 开发命令

### 开发模式

```bash
# H5 开发
pnpm dev:h5

# App 开发
pnpm dev:app
```

### 构建命令

```bash
# H5 构建
pnpm build:h5

# App 构建
pnpm build:app
```

## 代码规范

项目集成了以下代码规范工具：

- ESLint：JavaScript/TypeScript 代码检查
- Stylelint：CSS/SCSS 样式检查
- Prettier：代码格式化
- commitlint：Git 提交信息规范

## Git 工作流

项目使用 simple-git-hooks 和 lint-staged 进行提交前代码检查，使用 commitizen (cz-git) 规范化提交信息。

提交代码推荐使用：

```bash
pnpm commit
```

## 项目特性

1. **自动路由**：使用 @uni-helper/vite-plugin-uni-pages 自动生成路由配置
2. **自动导入**：使用 unplugin-auto-import 自动导入 API
3. **布局系统**：使用 @uni-helper/vite-plugin-uni-layouts 支持布局系统
4. **组件自动注册**：使用 @uni-helper/vite-plugin-uni-components 自动注册组件
5. **图标系统**：使用 unplugin-icons 支持图标系统
6. **原子化 CSS**：使用 UnoCSS 支持原子化 CSS
7. **类型安全**：完整的 TypeScript 类型支持

## 最佳实践

1. 使用组合式 API
2. 使用 Pinia 进行状态管理
3. 使用 vue-query 处理异步数据和缓存
4. 使用 composables 抽象可复用逻辑
5. 遵循项目的文件命名和组织约定
6. 使用环境变量区分开发/生产配置
7. vue 文件中使用 `<script setup lang="ts">` `<template>` `<route>` 和 `<style scoped lang="scss">` 语法，同时也按照这个顺序编写代码，先 script，再 template，route，最后是 style
8. 页面布局和内容优先使用 UniApp 的基础组件 `view`, `text`, 和 `image` 标签进行构建，以保证最佳的跨端兼容性和性能。仅在基础组件无法满足复杂交互或特定 UI 需求时，才考虑使用更复杂的组件库或自定义组件。

## 注意事项

1. 不同平台可能存在兼容性差异，请参考 UniApp 文档
2. 使用条件编译处理平台差异 `#ifdef` 和 `#endif`
3. App 开发需要配置原生插件和权限

## 业务代码

1. 以下组件优先使用 wot-design-uni:
   - Popup
   - Segmented
   - Badge
   - Form
   - Button: round 设置为 false
2. 引用 wot-design-uni 组件库的组件时，不需要在 router 区块中使用 usingComponents。
3. 列表使用 z-paging 组件。
4. 调用 uniapp 方法，优先使用`@uni-helper/uni-promises`中的方法
4. toast使用wot的toast组件

## 导航栏使用规则

1. 如果页面不需要在导航栏右侧添加自定义按钮，优先使用UniApp自带的导航栏
   - 在route配置中设置`"navigationStyle": "default"`和`"navigationBarTitleText": "页面标题"`
   - 不需要手动添加Navbar组件
2. 如果页面需要在导航栏右侧添加自定义按钮或其他复杂交互，则使用Navbar组件：
   - 在route配置中设置`"navigationStyle": "custom"`
   - 手动引入Navbar组件
   - 通过Navbar组件的`#right`插槽添加自定义内容

## CSS 类名和 UnoCSS 的使用建议

1. 优先使用 BEM 规范（小写字母和短横线）为具有明确结构和意义的元素创建自定义 CSS 类。
2. 当 BEM 类能清晰表达样式时，避免直接在模板中使用 UnoCSS 原子类。
3. 在层级较深(命名超过两层，比如 block\_\_element-element)或需要简单工具性样式（布局、间距、简单颜色等）时，推荐使用 UnoCSS 原子类以提高效率。 目标是在 BEM 的结构化和 UnoCSS 的便利性之间取得平衡，编写清晰、可维护的代码。
4. 如果使用了自定义类，但是没有使用到 CSS 代码，并且没有和子节点形成 BEM 的分组，建议删除该类。
