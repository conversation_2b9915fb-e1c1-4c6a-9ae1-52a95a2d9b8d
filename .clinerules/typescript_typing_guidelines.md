# TypeScript 类型定义管理指南

本文档旨在为本项目中 TypeScript 类型的统一管理提供指导原则，确保代码库的清晰、可维护和类型安全。

## 1. 核心原则

- **集中与分散结合**: 高度共享的类型（如核心业务实体、通用工具类型）应集中管理。特定模块或组件的局部类型可就近定义。
- **清晰的职责划分**: 不同类型的定义应放置在约定好的目录结构中。
- **DRY (Don't Repeat Yourself)**: 尽可能复用已有的类型定义。

## 2. 推荐的类型组织结构

```
src/
├── api/                # API 请求函数
├── components/         # 全局共享组件 (若有)
├── pages/              # 页面组件
│   └── [pageName]/
│       ├── components/ # 页面级组件
│       └── index.vue
├── service/
│   ├── types.ts        # 服务层相关的通用类型 (如API标准响应结构)
│   └── index.ts        # 服务层入口
├── store/              # Pinia 状态管理
│   └── modules/
│       └── [storeName]/
│           ├── index.ts
│           └── types.ts  # 特定 store 模块的状态、Action Payload 类型
├── types/                # ✨ 主要的共享类型定义目录
│   ├── index.ts          # (可选) 用于重新导出，方便统一引入
│   ├── api/              # (可选子目录) 或直接在 types/ 下按实体命名
│   │   └── [entityName].ts # 例如: User.ts, Product.ts - 后端API数据实体模型
│   ├── shared.ts         # 项目内多处共享的通用简单类型、工具类型
│   ├── enums.ts          # 项目中使用的枚举
│   └── [domain].ts       # (可选) 特定业务领域的复杂模型
└── ...
typings/                  # 全局类型声明，第三方库扩展
├── common.d.ts         # 全局通用声明
├── shime-uni.d.ts      # UniApp 相关 shim
└── vite-env.d.ts       # Vite 环境变量类型
```

## 3. 不同场景的类型管理策略

### a. 后端返回的类型 (API 相关)

- **位置**:
  - 核心数据实体 (如 `UserProfile`, `Product`): 定义在 `src/types/[EntityName].ts` (例如 `src/types/User.ts`) 或 `src/types/api/[EntityName].ts`。
  - 通用 API 响应结构 (如 `ApiResponse`, `ApiPaginatedResponse`): 定义在 `src/service/types.ts`，并遵循 `.clinerules/biz_rules.md` 中的规范。
- **引用**: API 请求函数 (`src/api/`) 和服务层应从上述位置导入这些类型。

### b. View 层使用的类型 (组件 Props, Emits, 本地状态)

- **Props 和 Emits**:
  - 优先在 Vue 组件的 `<script setup lang="ts">` 内部使用 `defineProps<Interface>()` 和 `defineEmits<Interface>()` 直接定义。
  - 如果 Props 接口非常复杂或需要在组件外部引用，可以考虑在组件同级目录下创建 `types.ts` 文件，或者如果该类型是共享实体，则从 `src/types/` 导入。
- **本地响应式状态 (`ref`, `reactive`)**:
  - 直接在 `<script setup lang="ts">` 中通过泛型指定类型，如 `ref<MyType>()`。
  - 如果类型是共享实体，从 `src/types/` 导入。

### c. Store (Pinia) 使用的类型

- **位置**:
  - 为每个 Pinia store 模块创建一个 `types.ts` 文件 (例如 `src/store/modules/auth/types.ts`)。
  - 在此文件中定义该模块的 `State` 接口、`Action` 的 `payload` 类型等。
- **引用**:
  - 如果 Store 的状态部分是通用的业务实体 (如 `UserProfile`)，应从 `src/types/` 导入，而不是在 Store 的 `types.ts` 中重复定义。

### d. 枚举 (Enums)

- **位置**: 共享的枚举统一定义在 `src/types/enums.ts`。
- **示例**:
  ```typescript
  // src/types/enums.ts
  export enum OrderStatus {
    Pending = 'PENDING',
    Processing = 'PROCESSING',
    Shipped = 'SHIPPED',
    Delivered = 'DELIVERED',
    Cancelled = 'CANCELLED',
  }
  ```

## 4. `typings/` 目录

- 用于存放全局 `.d.ts` 声明文件。
- 包括对无类型第三方库的模块声明、UniApp 或 Vite 特有的环境类型扩展。
- 避免在此处定义项目具体的业务实体类型。

## 5. 导入与路径别名

- 推荐使用 `tsconfig.json` 中配置的路径别名 (如 `@/types/*`) 进行导入，以保持导入路径的简洁和一致性。
- 考虑在 `src/types/index.ts` 中重新导出该目录下的主要类型，以便通过 `@/types` 一次性导入多个常用类型。

## 6. 遵循 `.clinerules/biz_rules.md`

- API 相关的类型定义（尤其是响应结构）必须严格遵守 `biz_rules.md` 中关于接口返回格式的规定。

---
