# 使用 Mermaid 可视化 Swagger-to-Code 生成规则

# Swagger-to-Code 规则

**重要提示：** 通过此流程生成的代码，除了遵循以下专门的生成规则外，还必须严格遵守项目 `.clinerules/biz_rules.md` (业务规则) 和 `.clinerules/project.md` (项目架构指南) 中定义的各项规范。

**流程图 - 详细生成流程**:
根据提示词提取类型生成名称 modelName
根据提示词提取服务函数 apiName
根据提示词提取是否单独客户端 specialized, 默认 specialized = false

```mermaid
graph TD
    A[开始: 接收 Swagger JSON] --> B{"解析 Swagger JSON (api-temp.json)"};
    B --> C{提取接口核心信息};
    C --> D[HTTP方法, 路径, operationId, summary];
    C --> E{提取参数};
    E --> F[参数: name, in, required, type/schema];
    C --> J{"提取响应 (成功状态码)"};
    J --> K["响应 Schema ($ref?)"];

    subgraph "准备阶段"
        O["读取 .clinerules/* (项目规范)"];
        P[用户确认: host+basePath -> BaseUrl常量];
        P --> Q["确定 uni-network 实例:\n- **If specialized = true:** 根据接口路径前缀（例如，路径以 \`/merchant\` 开头的接口对应 \`merchantInstance\`，以 \`/hdsapi\` 开头的接口对应 \`hdsapiInstance\`）或在生成代码提示词中更具体的指令来选择固定的实例。\n- **If specialized = false:** 使用 \`src/service/index.ts\` 中的 \`getInstance()\` 方法。该方法会根据当前登录的用户类型动态返回相应的 \`hdsapiInstance\` 或 \`merchantInstance\`。"];
    end

    subgraph "类型生成 (src/types/api/)"
        K --> R{解析 Schema 定义};
        R -- $ref --> S[参考 api-docs.json definitions,提取ExecuteResult«T_Base»中的T_Base. 如果T_Base来源于$ref且包含\`LightOperation\`前缀, 则在生成TypeScript类型名时移除该前缀 (e.g., \`LightOperationWorkOrder\` -> \`WorkOrder\`, \`LightStation\` -> \`Station\`). **如果处理后的类型名以 \`Dto\` 结尾, 则移除 \`Dto\` (e.g., \`InspectionPlanStatisticsDto\` -> \`InspectionPlanStatistics\`)**];
        S --> T[生成基础响应类型 T_Base (已移除\`LightOperation\`前缀和末尾的\`Dto\`, 如适用)];
        R -- 直接定义 --> T;
        F --> U["生成请求参数类型:\n- 类型名通常基于 \`modelName\` (首字母大写) + Path的最后主要部分(首字母大写, 去除通用前缀如'light/operation/'后) + \`Params\` (e.g., modelName: Workorder, path: /workOrder/page -> \`WorkorderPageParams\`)\n- 移除所有名为 \`pageable.*\` 的字段\n- 不包含生成器自身的注释 (e.g., // Authorization header...)"];
        T --> V{响应是否分页?};
        V -- 是 --> W["包装: PaginatedContent<T_Base>(PaginatedContent在src/service/types.ts中)"];
        V -- 否 --> X[包装: T_Base];
        W --> Y[最终响应类型];
        X --> Y;
        U --> Z[最终请求类型];
        Y --> AA["写入类型文件: src/types/api/modelName.ts, modelName首字母大写.\n**如果文件已存在，则智能追加或合并新类型，确保不丢失原有类型及其JSDoc注释。\n所有类型（新生成的和已存在的）的属性都应包含来自 Swagger 'description' 字段的 JSDoc 注释，或根据项目规范补充注释。\n**生成的类型不含数据格式行内注释 (e.g., // int32, // date-time)."];
        Z --> AA;
    end

    subgraph "服务函数生成 (src/api/)"
        D --> AB["确定函数名: 基于HTTP方法(小写)和接口路径(移除通用前缀如'/light/operation/'后，转换为驼峰式拼接, e.g., \`GET /light/operation/workOrder/page\` -> \`getWorkOrderPage\`). \n**GET请求命名进一步处理:** 如果基于 \`operationId\` 生成的函数名以 \`Get\` 结尾, 则移除末尾的 \`Get\` (e.g., \`getInspectionWorkOrderGet\` -> \`getInspectionWorkOrder\`).\n**POST请求命名规则:**\n- 若路径包含占位符 (e.g., \`/path/{id}/action\`), 函数名格式为 \`actionEntity\` (e.g., \`/workOrder/{orderCode}/audit-pass\` -> \`auditPassWorkOrder\`).\n- 若路径含批量操作 (e.g., \`/path/batch-action\`), 函数名格式为 \`batchActionEntity\` (e.g., \`/workOrder/batch-dispatch\` -> \`batchDispatchWorkOrder\`).\n**If specialized = false:**\n- 在每个服务函数内部定义 \`const urlMap = {};\`。\n- 根据当前 Swagger 文件的 \`basePath\` 初始化 \`urlMap\` (e.g., \`basePath: /merchant\` -> \`urlMap.merchant = '/path/from/swagger'\; \`basePath: /hdsapi\` -> \`urlMap.haier = '/path/from/swagger'\;)。只添加当前 Swagger 文件对应用户类型的路径到新生成的 \`urlMap\` 中。\n- 如果目标 API 文件中已存在同名函数且该函数已有 \`urlMap\`，则智能合并路径，保留其他用户类型的路径并更新当前 Swagger 文件对应用户类型的路径。\n- 如果 \`path\` 在多个 Swagger 文件中重复（对应不同用户类型），则合并路径到同一个函数的 \`urlMap\` 中。"];
        Q --> AD["选择请求实例 (e.g., merchantInstance)"];
        AA --> AE[导入所需类型];
        AD --> AE;
        AE --> AF{生成函数签};
        AF --> AG[参数: 使用请求类型. 如果参数用于请求体 (e.g., POST的query参数或body参数), 函数签名中的参数对象命名为 \`data\` (e.g., \`data: RequestType\`)];
        AF --> AH[返回: Promise<最终响应类型 (e.g., PaginatedContent<T> 或 T)>];
        AG --> AI{生成函数体};
        AH --> AI;
        AI --> AJ["调用实例方法:\n- **If specialized = true:** 使用固定实例 (见节点 Q) 和 Swagger 中定义的原始路径 (e.g., \`fixedInstance.post('/original/path', data)\`).\n- **If specialized = false:**\n  - 函数体内部包含:\n    \`const userStore = useUserStore();\`\n    \`const userType = userStore.userInfo?.userType;\`\n    \`// const urlMap = { ... }; (根据节点 AB 规则生成)\`\n    \`const path = urlMap[userType];\`\n  - 调用 \`getInstance().<method>(path, payload, config)\` 时不使用显式泛型。\n  - 函数直接 \`return await getInstance().<method>(path, payload, config);\`。**注意：** 此处能够直接返回，是因为项目中统一的请求拦截器已经处理了外层 \`ApiResponse\` 的解包，并直接返回了 \`result\` 字段中的数据。因此，服务函数的返回类型应为解包后的数据类型（如 \`PaginatedContent<T>\`），而不是 \`ApiPaginatedResponse<T>\`。\n  - \`payload\` 对于POST/PUT等方法是 \`data\` 参数；对于GET方法，如果存在查询参数，则是 \`{ params }\` (使用对象属性简写)。***对于没有请求体的 POST 请求，\`payload\` 参数可以省略或传递 \`undefined\`。***"];
        F --> AK{参数位置?};
        AK -- query --> AL[如果是post, 使用'data'字段,并加上@/service/types中ContentType.APPLICATION_X_WWW_FORM_URLENCODED, 否则使用 'params' 字段];
        AK -- body --> AM[使用 'data' 字段];
        AL --> AJ;
        AM --> AJ;
        AJ --> AN[写入服务文件: src/api/apiName.ts, 如果文件已经存在，则追加. 生成的函数不含数据格式行内注释];
    end

    AN --> AO[结束: 代码生成完毕];

    classDef process fill:#f9f,stroke:#333,stroke-width:2px;
    classDef io fill:#ccf,stroke:#333,stroke-width:2px;
    classDef decision fill:#cfc,stroke:#333,stroke-width:2px;
    classDef rule fill:#ff9,stroke:#333,stroke-width:2px;

    class A,AO io;
    class B,C,E,J,R,S,T,U,V,AB,AC,AD,AE,AF,AG,AH,AI,AJ,AK,AL,AM,AN process;
    class G,V,AK decision;
    class L,M,N,O,P rule;
