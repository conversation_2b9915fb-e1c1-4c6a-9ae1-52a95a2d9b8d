# 从 MasterGo 和 UI 生成页面代码的工作流程

以下 Mermaid 图表描述了根据 MasterGo 设计链接和 UI 图片参考，生成包含接口逻辑的页面代码的完整流程。
**重要提示：** 通过此流程生成的代码，除了遵循以下专门的生成规则外，还必须严格遵守项目 `.clinerules/biz_rules.md` (业务规则) 和 `.clinerules/project.md` (项目架构指南) 中定义的各项规范。

```mermaid
graph LR
    subgraph "输入 (Inputs)"
        direction LR
        A[MasterGo 设计链接 (可选)]
        B[UI 设计截图 (可选)]
        API_File[API 文件路径 (可选)]
        Template_Params[页面模板参数 (可选)]
    end

    subgraph "处理与分析 (Processing & Analysis)"
        direction TB
        C{1. 获取 MasterGo DSL}
        A --> C
        C -- DSL 数据 --> D{2. 分析设计稿结构与元素}
        D -- 设计细节 --> E{3. 参考 UI 截图进行视觉校对}
        B --> E
    end

    subgraph "代码生成 (Code Generation)"
        direction TB
        D --> SearchTemplates{3.5. 搜索模板提示语/规则库或原始资产}
        SearchTemplates -- "单个匹配模板/资产 或 无模板但有资产" --> F[4. 生成/复用页面模板 (Vue/HTML)]
        SearchTemplates -- "多个匹配的模板提示语/规则" --> UserSelectTemplate{3.6. 用户选择适用模板}
        UserSelectTemplate -- "已选模板提示语/规则" --> F
        SearchTemplates -- "无任何匹配" --> F
        E --> F
        D -- 样式信息 --> G[5. 生成/复用样式 (CSS/SCSS/UnoCSS)]
        SearchTemplates -- "模板/资产含样式" --> G
        D -- 数据与交互点 --> H{6. 识别所需 API 接口及数据模型}
        H -- "所需接口列表" --> H_API{6.5. 查找并集成项目API定义 (e.g., src/api/user.ts)}
        F --> I[7. 组合模板与样式]
        H_API -- "可用API函数" --> J{8. 实现 API 调用与数据绑定逻辑}
        I & J --> K[9. 集成业务逻辑与状态管理 (若需要)]
        K --> PageConfig[10. 生成页面配置 (UniApp route 块)]
    end

    subgraph "输出 (Output)"
        direction LR
        L[最终生成的页面代码 (Vue 文件)]
    end

    PageConfig --> L

    classDef inputStyle fill:#D6EAF8,stroke:#2E86C1,stroke-width:2px,color:#000
    classDef processStyle fill:#D1F2EB,stroke:#1ABC9C,stroke-width:2px,color:#000
    classDef codegenStyle fill:#FCF3CF,stroke:#F1C40F,stroke-width:2px,color:#000
    classDef reuseStyle fill:#E8DAEF,stroke:#8E44AD,stroke-width:2px,color:#000
    classDef outputStyle fill:#FADBD8,stroke:#E74C3C,stroke-width:2px,color:#000

    class A,B,API_File,Template_Params inputStyle;
    class C,D,E processStyle;
    class F,G,H,H_API,I,J,K codegenStyle;
    class SearchTemplates, UserSelectTemplate reuseStyle;
    class L outputStyle;
```

## 流程说明

1.  **输入 (均为可选)**:

    - **MasterGo 设计链接 (可选)**: 指向具体设计稿的 URL。如果提供，用于提取 DSL (设计系统语言) 以便精确分析设计。
    - **UI 设计截图 (可选)**: 作为视觉参考。如果提供，用于辅助理解设计意图、进行视觉校对，或在缺少 MasterGo 链接时尝试进行基本的结构分析。
    - **API 文件路径 (可选)**: 指向项目中一个或多个具体的 API 定义文件（例如 `src/api/user.ts`）。如果提供，系统在集成 API 逻辑时将优先或仅从这些指定文件中查找。
    - **页面模板参数 (可选)**: 一个对象或键值对集合。当与“模板提示语/规则”一同使用时——此处的“模板提示语/规则”特指下文第 3 点“代码生成”中“**优先查找模板提示语/规则**”部分所详述的：那些在预定义的“模板提示语/规则库”（例如，存储在 `templates/` 目录下，每个模板一个 `.md` 文件，如 `search-page-template.md`）中定义的，包含元素结构、JS 逻辑框架和交互模式的预设蓝图——这些参数将用于指导从这些蓝图中选择变体、配置其内部选项或决定包含哪些可选特性。在其他情况下（如直接复用原始资产或从头生成），它们也可能用于常规的页面定制（例如，指定列表页面的列配置、表单页面的字段定义等）。这些参数会影响模板的具体实例化方式。
    - _注意: 至少需要提供一种输入（如 MasterGo 链接、UI 截图、明确的页面类型/模板选择或页面模板参数）以指导代码生成。若所有输入均缺失，系统可能无法自动生成有意义的代码，或需要用户提供更明确的指令。_

2.  **处理与分析**:

    - **获取 MasterGo DSL**: 此步骤仅在提供了 **MasterGo 设计链接** 时执行。如果未提供，则跳过此步骤。
    - **分析设计稿结构与元素**:
      - 如果执行了上一步（获取 MasterGo DSL），则主要基于 DSL 进行详细分析，识别页面布局、组件、文本内容、颜色、字体、间距等。
      - 如果未提供 MasterGo 链接，但提供了 **UI 设计截图**，系统可能会尝试从截图中提取基本的视觉结构和元素信息（此分析的深度和准确性可能受限）。
      - 如果两者均未提供，此步骤的分析将非常有限，后续流程将更依赖用户直接选择的模板、资产或提供的其他指令。
    - **参考 UI 截图进行视觉校对**: 此步骤仅在提供了 **UI 设计截图** 时执行。用于辅助验证和调整基于 DSL 或其他来源生成的结构和样式。

3.  **代码生成**:

    - **搜索模板提示语/规则库或原始资产**:
      - 此步骤的有效性高度依赖于“处理与分析”阶段的输出。
      - 如果分析结果充分（如有 DSL），搜索将更精确。
      - 如果分析结果有限（例如，仅有 UI 截图或无设计输入），搜索可能更多地依赖于用户直接指定的页面名称、功能描述，或引导用户直接选择一个已有的模板/资产。
      - **优先查找模板提示语/规则**: 在预定义的“模板提示语/规则库”（例如，存储在 `templates/` 目录下，每个模板一个 `.md` 文件，如 `monitor-list-page-template.md`）中搜索匹配的条目。这些提示语是现有页面或组件经过结构化处理的蓝图，包含元素结构、JS 逻辑框架和交互模式的预设。
      - **用户选择适用模板**: 如果搜索到多个匹配的模板提示语/规则，系统将列表展示，并请求用户选择最适用的一个。
      - **若无匹配提示语/规则或用户未选择，则查找原始资产 (组件/代码片段/页面模板)**:
        - **目标性搜索**: 在约定的组件目录（如 `src/components/`, `src/uni_modules/`）、页面目录（如 `src/pages/`）或根据项目结构和命名规范进行查找。
        - **智能匹配**: 利用组件名、页面结构相似性、props 定义、功能性注释或元数据进行匹配。
      - **索引/缓存机制 (可选)**: 对于大型项目，可考虑预先建立提示语/规则或原始资产的索引以加速查找。
      - **人工辅助建议**: 系统可提供高度相似的候选原始资产列表，由开发者确认。
    - **生成/复用页面模板**:
      - 生成和复用逻辑将根据可用的输入（DSL 分析结果、UI 分析结果、选定的模板/资产、**页面模板参数**）进行调整。
      - 如果输入信息不足（例如，无 MasterGo 设计，无 UI 截图，且未选择明确模板或提供参数），生成的模板可能非常基础或需要用户提供更多结构性指导。
      - **如果用户选择了或系统找到了单个匹配的“模板提示语/规则” (来自 `templates/` 等位置)**:
        - **实例化与定制**: 以选定或找到的提示语/规则（通常是一个 `.md` 文件，详细描述了结构、逻辑和样式指导）为蓝图。此时，**页面模板参数**将用于指导如何从提示语/规则中选择变体、配置其内部参数，或决定包含哪些可选特性。然后，结合新的 MasterGo 设计稿的具体参数（如文本内容、特定尺寸、颜色、交互行为等）进行填充和定制，生成最终的页面模板。
        - **JS 逻辑集成**: 提示语/规则中封装的 JS 逻辑框架（如数据获取模式、事件处理框架）将被实例化。**页面模板参数**同样会影响此处的逻辑集成，例如，根据参数决定是否启用某些数据处理逻辑或事件监听器。最终，根据新页面的具体 API 和数据需求进行调整和填充。
      - **如果未选择/找到模板提示语/规则，但找到可复用的原始页面模板**:
        - **复制与修改**: 将现有相似页面的代码作为新页面的基础模板。
        - **结构调整**: 根据新的 MasterGo 设计稿，对复制的模板进行结构上的增删改。
        - **内容替换**: 更新模板中的静态文本、图片引用等。
        - **逻辑适配**: 调整或替换原有的数据绑定、事件处理和业务逻辑。
      - **如果未选择/找到模板提示语/规则，也未找到可复用页面模板，但找到可复用的原始单个组件**:
        - **集成**: 将组件以其标签名插入到新页面的模板中。
        - **Props 映射**: 将 MasterGo 设计稿属性映射到组件 `props`。
        - **事件处理**: 绑定事件监听器。
        - **适配调整**: 按需调整。
      - **如果未找到任何可复用提示语/规则或原始资产 (生成全新模板)**:
        - **结构构建**: 基于 MasterGo DSL 生成 HTML 元素和 Vue 组件结构。
        - **内容填充**: 填充文本内容。
        - **占位符与插槽**: 预留动态数据绑定点和插槽。
        - **基本属性**: 设置 HTML 属性。
    - **生成/复用样式**:
      - 样式的生成/复用同样依赖于可用的设计输入。
      - 若缺少 MasterGo DSL 和 UI 截图，样式可能主要来自选用的模板/资产，或需要更多手动编码和 UnoCSS 原子类的应用。
      - **基于提示语/规则**: 如果使用了模板提示语/规则，其通常会包含关联的样式定义或指向通用样式的引用，直接应用或按需调整。
      - **基于原始资产**: 优先复用已有组件或页面的样式。
      - **通用与新建**: 利用项目中已定义的通用样式类（如 UnoCSS 预设、全局 SCSS mixins/variables）。对于新组件或特定样式，提取或转换设计稿中的样式信息为 CSS/SCSS 或 UnoCSS 原子类。
    - **识别所需 API 接口及数据模型**:
      - 如果 MasterGo DSL 或 UI 截图（或用户选择的模板）提供了关于数据和交互的线索，将用于识别 API 需求。
      - 否则，此步骤可能依赖于用户提供的 **API 文件路径**中的接口，或需要用户更明确地指定所需功能。
    - **查找并集成项目 API 定义 (e.g., src/api/user.ts)**:
      - 如果在输入中提供了 **API 文件路径**，系统将优先扫描这些指定的文件来查找所需的 API 函数。
      - 如果未提供具体的 API 文件路径，或者在指定文件中未找到所有必需的接口，系统会扫描默认的 `src/api/` 目录下的 TypeScript 文件（如 `src/api/user.ts`, `src/api/workorder.ts` 等）。
      - 根据上一步识别出的 API 功能需求（可能来自 DSL、UI 分析、模板预设或用户直接指令），系统会尝试在这些文件中找到对应的导出函数。
      - 如果前期输入不足导致 API 需求不明确，此步骤可能需要用户提供更具体的指导或手动选择接口。
      - 找到匹配的 API 函数后，会准备在页面代码中自动导入并使用它们。
    - **组合模板与样式**: 将生成的或复用的模板和样式结合起来。
    - **实现 API 调用与数据绑定逻辑**: 编写 JavaScript/TypeScript 代码。此过程将**自动导入**在上一步中找到的 API 函数。然后，使用这些导入的函数来处理 API 请求、管理响应数据，并将数据绑定到页面模板上。例如，如果识别到需要 `login` 接口，并且在 `src/api/user.ts` (或用户指定的 API 文件) 中找到了 `login` 函数，代码生成器会自动添加相应的 `import` 语句并调用 `login()`。
    - **集成业务逻辑与状态管理**: 根据需要，添加页面特有的业务逻辑，或与 Pinia 等状态管理工具集成。
    - **生成页面配置 (UniApp route 块)**: 为 UniApp 页面生成正确的 `<route>` 配置块。
      - **格式**: 遵循 UniApp 的 `pages.json` 中 `style` 节点的配置方式。例如：
        ```html
        <route lang="json">
          { "style": { "navigationStyle": "default", // 或 'custom' "navigationBarTitleText":
          "页面标题" // ... 其他页面特有配置 } }
        </route>
        ```
      - **内容**: `navigationBarTitleText` 应根据设计稿或页面功能设定。其他配置项（如 `enablePullDownRefresh` 等）按需添加，`navigationStyle`这个节点默认添加，默认为 `default`。
    - **注释**: 严格遵守 `.clinerules/biz_rules.md` 中的注释规则。移除所有不符合规则的注释，特别是描述代码显而易见功能、开发过程或应通过代码自解释的注释。

4.  **输出**:
    - **最终生成的页面代码**: 一个完整的、可运行的 Vue 组件文件，包含了结构、样式、页面配置和业务逻辑。其完整度和准确性将取决于所提供输入信息的质量和数量，并严格遵循项目规范。
    - **项目规范遵循**: 生成的代码必须严格遵守 `.clinerules/project.md` 中定义的项目架构指南和最佳实践。
