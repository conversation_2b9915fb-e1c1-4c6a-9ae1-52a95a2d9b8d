/// <reference types="vite/client" />
/// <reference types="@dcloudio/types" />

type ProxyType = 'dev' | 'test' | 'prod'

interface ProxyConfig {
  /** 匹配代理的前缀，接口地址匹配到此前缀将代理的target地址 */
  prefix: string
  /** 代理目标地址，后端真实接口地址 */
  target: string
  /** 路径重写 */
  rewrite?: (path: string) => string
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_PORT: number
  readonly VITE_USE_PROXY?: boolean
  readonly VITE_PROXY_TYPE?: ProxyType
  readonly VITE_USE_HASH: 'true' | 'false'
  readonly VITE_BASE_API: string
  readonly VITE_BUGLY_ANDROID_APP_ID: string
  readonly VITE_BUGLY_IOS_APP_ID: string
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue'

  const component: DefineComponent<object, object, any>
  export default component
}

declare module '@/uni_modules/*'
