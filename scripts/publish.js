const fs = require('node:fs')
const path = require('node:path')
const os = require('node:os')
const { exec } = require('node:child_process')
const prompts = require('prompts')

const getManifestVersion = () => {
  const manifestPath = path.resolve(process.cwd(), 'manifest.config.ts')
  if (!fs.existsSync(manifestPath)) {
    console.error(
      '[publish-script] Error: manifest.config.ts 文件未找到。',
    )
    process.exit(1)
  }
  const manifestContent = fs.readFileSync(manifestPath, 'utf-8')
  const versionNameMatch = manifestContent.match(/versionName:\s*'([^']*)'/)
  const versionCodeMatch = manifestContent.match(/versionCode:\s*'([^']*)'/)
  return {
    versionName: versionNameMatch ? versionNameMatch[1] : '1.0.0',
    versionCode: versionCodeMatch ? versionCodeMatch[1] : '100',
  }
}

const generateVersionChoices = (currentVersion) => {
  const [major, minor, patch] = currentVersion.split('.').map(Number)
  return [
    { title: `${major}.${minor}.${patch} (保持不变)`, value: `${major}.${minor}.${patch}` },
    {
      title: `${major}.${minor}.${patch + 1} (补丁)`,
      value: `${major}.${minor}.${patch + 1}`,
    },
    { title: `${major}.${minor + 1}.0 (次版本)`, value: `${major}.${minor + 1}.0` },
    { title: `${major + 1}.0.0 (主版本)`, value: `${major + 1}.0.0` },
  ]
};

(async () => {
  const currentVersions = getManifestVersion()
  const versionNameChoices = generateVersionChoices(currentVersions.versionName)
  const currentVersionCodeNum = Number.parseInt(currentVersions.versionCode, 10)
  const versionCodeChoices = [
    { title: `${currentVersionCodeNum} (保持不变)`, value: String(currentVersionCodeNum) },
    {
      title: `${currentVersionCodeNum + 1} (递增)`,
      value: String(currentVersionCodeNum + 1),
    },
  ]

  const response = await prompts([
    {
      type: 'select',
      name: 'certificate',
      message: '请选择证书类型:',
      choices: [
        { title: 'Local', description: '用于本地开发测试', value: 'local' },
        { title: 'Haier', description: '用于正式发布', value: 'haier' },
      ],
    },
    {
      type: 'select',
      name: 'env',
      message: '请选择构建环境:',
      choices: [
        { title: 'Development', description: '开发环境', value: 'development' },
        { title: 'Staging', description: '预发布环境', value: 'staging' },
        { title: 'Production', description: '生产环境', value: 'production' },
      ],
    },
    {
      type: 'select',
      name: 'newVersionName',
      message: '请选择版本名:',
      choices: versionNameChoices,
    },
    {
      type: 'select',
      name: 'newVersionCode',
      message: '请选择版本号:',
      choices: versionCodeChoices,
    },
  ])

  const { certificate, env, newVersionName, newVersionCode } = response

  if (!certificate || !env || !newVersionName || !newVersionCode) {
    console.error('[publish-script] Error: 用户取消了选择。')
    process.exit(1)
  }

  function updateManifest(versionName, versionCode) {
    console.log('[publish-script] 正在更新 manifest.config.ts...')
    const manifestPath = path.resolve(process.cwd(), 'manifest.config.ts')
    let manifestContent = fs.readFileSync(manifestPath, 'utf-8')

    manifestContent = manifestContent.replace(
      /versionName:\s*'.*?'/,
      `versionName: '${versionName}'`,
    )
    console.log(`[publish-script] versionName 更新为: ${versionName}`)

    manifestContent = manifestContent.replace(
      /versionCode:\s*'.*?'/,
      `versionCode: '${versionCode}'`,
    )
    console.log(`[publish-script] versionCode 更新为: ${versionCode}`)

    fs.writeFileSync(manifestPath, manifestContent, 'utf-8')
    console.log('[publish-script] manifest.config.ts 更新成功。')
  }

  updateManifest(newVersionName, newVersionCode)

  const handleArtifacts = (packagePaths, certificate) => {
    if (!packagePaths.length) {
      console.error('[publish-script] Error: 未能从日志中获取任何安装包路径。')
      return
    }
    console.log('[publish-script] 开始处理打包产物...')
    const { versionName, versionCode } = getManifestVersion()
    const downloadsPath = path.join(os.homedir(), 'Downloads')

    if (!fs.existsSync(downloadsPath)) {
      fs.mkdirSync(downloadsPath, { recursive: true })
      console.log(
        `[publish-script] 创建下载目录于: ${downloadsPath}`,
      )
    }

    const buildType = certificate === 'haier' ? 'release' : 'debug'


    for (const sourcePath of packagePaths) {
      console.log(`[publish-script] 正在处理: ${sourcePath},${JSON.stringify(packagePaths)}`)
      // if (!fs.existsSync(sourcePath)) {
      //   console.warn(
      //     `[publish-script] 警告: 路径 ${sourcePath} 不存在, 跳过处理。`,
      //   )
      //   continue
      // }

      // const ext = path.extname(sourcePath)
      // if (!ext) {
      //   console.warn(
      //     `[publish-script] 警告: 无法从路径 ${sourcePath} 获取文件扩展名, 将使用当前时间戳作为扩展名。`,
      //   )
      // }
      // const now = new Date()
      // const year = now.getFullYear()
      // const month = (`0${now.getMonth() + 1}`).slice(-2)
      // const day = (`0${now.getDate()}`).slice(-2)
      // const hours = (`0${now.getHours()}`).slice(-2)
      // const minutes = (`0${now.getMinutes()}`).slice(-2)
      // const timestamp = `${year}${month}${day}${hours}${minutes}`
      // const newFileName = `${certificate}energy-${buildType}-v${versionName}+${timestamp}${ext}`
      // const destPath = path.join(downloadsPath, newFileName)

      // try {
      //   fs.copyFileSync(sourcePath, destPath)
      //   console.log(
      //     `[publish-script] 产物已复制并重命名: ${destPath}`,
      //   )
      // }
      // catch (e) {
      //   console.error(
      //     `[publish-script] Error: 复制文件失败 从 ${sourcePath} 到 ${destPath}`,
      //     e,
      //   )
      // }
    }
  }

  const packAndExit = (restoreEnvCallback) => {
    console.log('\n[publish-script] 开始云打包...')

    const configFileName = `configure_${certificate}.json`
    const configFilePath = path.resolve(
      process.cwd(),
      'publish',
      configFileName,
    )
    const hbuilderCliPath = '/Applications/HBuilderX.app/Contents/MacOS/cli'

    if (!fs.existsSync(configFilePath) || !fs.existsSync(hbuilderCliPath)) {
      console.error(
        `[publish-script] Error: 打包配置文件或 HBuilderX CLI 未找到。`,
      )
      process.exit(1)
    }

    const packCommand = `"${hbuilderCliPath}" pack --config "${configFilePath}"`
    console.log(`[publish-script] 正在执行打包命令: \n${packCommand}\n`)

    const packProcess = exec(packCommand)

    let successCount = 0
    const packagePaths = []
    let envRestored = false

    const handleData = (data) => {
      const output = data.toString()
      process.stdout.write(output)

      if (restoreEnvCallback && !envRestored && output.includes('向云端发送打包请求')) {
        restoreEnvCallback()
        envRestored = true
      }

      const isSuccess = output.includes("打包成功    安装包位置")
      if (isSuccess) {
        successCount += 1
        console.log('successCount: ',successCount,',output', output)
      }

      const pathRegex = /(?:安装包位置)：(.+\.(?:apk|ipa))/g
      if(isSuccess) {
        let match
        while ((match = pathRegex.exec(output)) !== null) {
          const packagePath = match[1].trim()
          if (packagePath.includes('/dist/') && !packagePaths.includes(packagePath)) {
            console.log(`[publish-script] 发现安装包: ${packagePath}`)
            packagePaths.push(packagePath)
          }
        }
      }
    }

    packProcess.stdout.on('data', handleData)
    packProcess.stderr.on('data', handleData)

    packProcess.on('exit', (packCode) => {
      if (restoreEnvCallback && !envRestored)
        restoreEnvCallback()

      if (packCode === 0 && packagePaths.length >= 2) {
        const { versionName, versionCode } = getManifestVersion()
        console.log(
          `\n[publish-script] 全部 (${successCount}) 打包任务成功完成。 证书: ${certificate}, 环境: ${env}, 版本名: ${versionName}, 版本号: ${versionCode}`,
        )
        handleArtifacts(packagePaths, certificate)
      }
      else {
        console.error(
          `\n[publish-script] 打包流程未完全成功。退出码: ${packCode}, 成功任务数: ${successCount}。`,
        )
      }
      // process.exit(packCode ?? 0)
    })
  }

  if (env === 'production') {
    packAndExit()
  }
  else {
    const prodEnvPath = path.resolve(process.cwd(), '.env.production')
    const sourceEnvPath = path.resolve(process.cwd(), `.env.${env}`)

    if (!fs.existsSync(sourceEnvPath)) {
      console.error(
        `[publish-script] Error: 环境配置文件未找到: ${sourceEnvPath}`,
      )
      process.exit(1)
    }

    let originalProdEnv = ''
    try {
      originalProdEnv = fs.readFileSync(prodEnvPath, 'utf-8')
    }
    catch (e) {
      if (e.code === 'ENOENT')
        console.log('[publish-script] .env.production 不存在, 将在操作后删除。')
      else throw e
    }

    const restoreEnv = () => {
      console.log('[publish-script] 正在恢复原始 .env.production...')
      if (originalProdEnv) {
        fs.writeFileSync(prodEnvPath, originalProdEnv, 'utf-8')
        console.log('[publish-script] .env.production 已恢复。')
      }
      else {
        try {
          fs.unlinkSync(prodEnvPath)
          console.log(
            '[publish-script] .env.production 已移除 (因其原始不存在)。',
          )
        }
        catch (e) {
          if (e.code !== 'ENOENT')
            throw e
        }
      }
    }

    console.log(
      `[publish-script] 正在备份 .env.production 并切换到 .env.${env}...`,
    )
    const sourceEnvContent = fs.readFileSync(sourceEnvPath, 'utf-8')
    fs.writeFileSync(prodEnvPath, sourceEnvContent, 'utf-8')
    console.log(`[publish-script] 已切换到 .env.${env}。`)

    packAndExit(restoreEnv)
  }
})()
