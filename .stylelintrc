{"defaultSeverity": "error", "extends": ["stylelint-config-standard", "stylelint-config-standard-scss", "stylelint-config-recommended-vue", "stylelint-config-html", "stylelint-config-recess-order"], "plugins": ["stylelint-order"], "rules": {"selector-class-pattern": ["^([#a-z][$#{}a-z0-9]*)((-{1,2}|_{2})[$#{}a-z0-9]+)*$", {"message": "Expected class selector to be kebab-case"}], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["::v-deep", "deep", "v-deep"]}], "selector-type-no-unknown": [true, {"ignoreTypes": ["page", "rich-text", "scroll-view"]}], "unit-no-unknown": [true, {"ignoreUnits": ["rpx", "upx"]}], "no-descending-specificity": null, "no-empty-source": null, "keyframes-name-pattern": "^[a-z]+([A-Z][a-z]*)*$"}, "ignoreFiles": ["node_modules", "dist", "public", "output", "coverage", "temp", "*.js", "*.cjs", "*.mjs", "*.ts", "*.tsx", "*.svg", "*.gif", "*.md"]}