{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "DOM.Iterable", "ESNext"], "useDefineForClassFields": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["src/*"], "~/*": ["./*"]}, "resolveJsonModule": true, "types": ["vite/client", "@dcloudio/types", "@mini-types/alipay", "miniprogram-api-typings", "unplugin-vue-macros/macros-global", "unplugin-icons/types/vue", "type-fest", "@uni-helper/uni-types", "wot-design-uni/global", "z-paging/types"], "strict": true, "noImplicitThis": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "verbatimModuleSyntax": true, "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-types/volar-plugin"]}, "include": ["typings/components.d.ts", "typings/augmentation.d.ts", "**/*.d.ts", "**/*.tsx", "**/*.ts", "**/*.vue", "package.json"], "exclude": ["**/*.n<PERSON>"]}